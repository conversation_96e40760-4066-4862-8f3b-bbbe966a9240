{"SUPER_AI": {"try_again": "Tentar novamente", "free_credits": "Após os 5 créditos, você poderá configurar sua própria chave do Google Gemini, ganhar 300 dólares de crédito pela Google e usar ilimitado!", "an_last_execution_error": "Na sua última construção, as seguintes execuções deram erro. Corrija apenas elas pois todas as outras já foram construídas:", "attention": "Atenção!", "an_error_occurred": "A execução teve erros,", "last_question_error": "Para a última pergunta, eu recebi o erro a seguir. Por favor, pense passo a passo para resolver isso.", "error_occurred": "Ocorreu um erro", "keep_calming": "<PERSON>que tranquilo, as vezes a IA responde algo que não esperávamos, repita sua pergunta e provavelmente vai dar certo.", "thoughts_time": "Pen<PERSON>u por", "valid_objects": "<PERSON><PERSON><PERSON><PERSON>", "building_in_real_time": "Construindo em Tempo Real", "no_data_available": "Não tenho informações suficientes para responder a sua pergunta.", "deep_thinking": "Pensar profundo", "deep_thinking_descr": "Pode demorar até 2 minutos, porém tem uma acurácia mais alta.", "google_search": "Busca Google", "google_search_grounding_descr": "Habilita busca no Google para informações em tempo real e capacidades de pesquisa na web", "code_execution": "Execução de Código", "code_execution_descr": "Permite que a IA gere e execute código Python para análise de dados e documentos", "search_suggestions": "Sugestões de Busca", "web_search_results": "Resultados da Busca Web", "grounding_sources": "<PERSON><PERSON><PERSON>", "resource_exhausted": "Sua chave de API do Gemini deve ser vinculada a um projeto Tier 1 para funcionar com o modelo que utilizamos, que é o Gemini 2.5 Pro.", "clicking_here": "clicando aqui", "clicking_here_to_fix": "clique aqui para corrigir", "internalServerError": "<PERSON>rro <PERSON>no", "errorOcurred": "Ocorreu um erro", "opsErrorOcurred": "Ops! Algo deu errado", "toContinue": "Para continuar, tente novamente", "tryAgainOnly": "Tente novamente", "errorFetchingDetails": "Ocorreu um erro ao recuperar os detalhes do plano", "errorExecutingTool": "Ocorreu um erro ao executar a ferramenta", "noValidPlanItems": "Nenhum item válido encontrado no plano", "noValidPlanItemsDetails": "A resposta da IA não contém itens de plano válidos que possam ser executados. Tente reformular sua pergunta ou solicitar uma ação mais específica.", "payloadPlanParseError": "Erro ao processar dados da resposta da IA", "payloadPlanParseErrorDetails": "Não foi possível processar os dados da resposta da IA. Verifique se a resposta está no formato correto.", "jsonParsingError": "A resposta contém uma sintaxe de chaves inválidas", "jsonParsingErrorDetails": "Tente fazer a pergunta novamente ou crie um novo chat se o problema persistir.", "apiKeyInvalidTitle": "A sua chave do Gemini está sem recursos", "apiKeyInvalidDetails": "Atualize a chave e tente novamente.", "errorRateLimit": "Limite de requisições excedido. Tente novamente em alguns instantes.", "errorModelNotFound": "Verifique se o nome do modelo está correto e tente novamente.", "errorServiceUnavailable": "Serviço temporariamente indisponível. Tente novamente em instantes.", "errorTimeout": "Tempo limite excedido. Tente novamente ou reduza o tamanho da solicitação.", "errorInvalidArgument": "Parâmetros inválidos para o modelo. Verifique o modelo e os limites.", "errorApiKey": "Problema com a chave de API. Verifique suas configurações.", "errorGeneric": "Ocorreu um erro inesperado. Tente novamente.", "errorNetworkError": "Falha na conexão com o servidor. Verifique sua internet.", "errorPrematureStop": "Não obtivemos resposta do Gemini", "errorPayloadTooLarge": "O arquivo enviado é muito grande. Por favor, tente um arquivo menor.", "hintRateLimit": "Aguarde alguns instantes ou configure uma chave própria.", "hintModelNotFound": "Verifique se o nome do modelo está correto.", "hintServiceUnavailable": "Aguarde alguns instantes e tente novamente.", "hintTimeout": "Reduza o tamanho da solicitação ou tente novamente.", "hintInvalidArgument": "Verifique os parâmetros da requisição.", "hintApiKey": "Verifique suas configurações de chave de API.", "hintGeneric": "Tente novamente em alguns instantes.", "hintNetworkError": "Verifique sua conexão com a internet.", "hintPrematureStop": "A conexão foi interrompida durante o processamento.", "hintJsonParsingError": "Tente fazer a pergunta novamente ou crie um novo chat se o problema persistir.", "of": "de", "streamInterruptedTitle": "Não obtivemos resposta do Gemini", "streamInterruptedDetails": "A conexão com o Gemini foi interrompida durante o streaming. Tente novamente.", "since": "<PERSON><PERSON>", "executionPlan": "Plano executado", "input": "Input", "output": "Output", "copiedSuccess": "Copiado com sucesso", "clickToCopy": "Clique para copiar", "item": "<PERSON><PERSON>", "project_settings": "Configurações do projeto", "web_access": "Acesso Web:", "mobile_test": "Teste Mobile:", "use_code": "- Use o código", "in_mitra_apps": "no Mitra Apps", "scan_qr_code": "- Escaneie o QR code abaixo para baixar o app:", "ios": "iOS", "android": "Android", "courtesy_credits": "Créditos Cortesia", "ai_credits": "Créditos IA", "operations": {"tables": {"created": "<PERSON><PERSON><PERSON>", "createFailed": "<PERSON><PERSON><PERSON> ao criar <PERSON>", "updated": "Tabelas Atualizadas", "updateFailed": "Falha ao <PERSON><PERSON><PERSON><PERSON>", "removed": "Tabelas Removid<PERSON>", "removeFailed": "<PERSON><PERSON><PERSON> ao remover <PERSON>"}, "attributes": {"added": "Atributos Adicionados", "addFailed": "Falha ao adicionar Atributos", "updated": "Atributos Atualizados", "updateFailed": "Falha ao atualizar Atributos", "removed": "Atributos Removidos", "removeFailed": "Falha ao remover Atributos"}, "variables": {"added": "Variáveis <PERSON>", "addFailed": "Falha ao adicionar <PERSON>", "removed": "Variáveis <PERSON>", "removeFailed": "Falha ao remover <PERSON>"}, "forms": {"created": "Formulários Criados", "createFailed": "Falha ao criar <PERSON>", "modified": "Formulários Modificados", "modifyFailed": "Falha ao modificar Formulários", "removed": "Formulários Removid<PERSON>", "removeFailed": "Falha ao remover <PERSON>"}, "screens": {"created": "Telas Criadas", "createFailed": "Falha ao criar Telas", "updated": "Telas Atualizadas", "updateFailed": "Falha ao atualizar Telas", "removed": "Telas Removidas", "removeFailed": "Falha ao remover Telas"}, "dml": {"executed": "DML Executado", "executeFailed": "Falha ao executar DML"}, "ddl": {"executed": "DDL Executado", "executeFailed": "Falha ao executar DDL"}, "dbActions": {"added": "Ações de DB Adicionadas", "addFailed": "Falha ao adicionar Ações de DB", "removed": "Ações de DB Removidas", "removeFailed": "Falha ao remover Ações de DB"}, "actions": {"added": "Ações Adicionadas", "addFailed": "Falha ao adicionar Ações", "modified": "Ações Modificadas", "modifyFailed": "Falha ao modificar Ações"}, "component": {"removed": "Componente Removido", "removeFailed": "Falha ao remover Componente"}, "tableComponent": {"added": "Tabela Adicionada", "addFailed": "Falha ao adiciona<PERSON>", "modified": "Tabela Modificada", "modifyFailed": "Falha ao modificar Tabela"}, "graphComponent": {"added": "Gráfico Adici<PERSON>", "addFailed": "Falha ao adicionar Gráfico", "modified": "Gráfico Modificado", "modifyFailed": "Falha ao modificar Gráfico"}, "crudListComponent": {"added": "Lista CRUD Adicionada", "addFailed": "Falha ao adicionar Lista CRUD", "modified": "Lista CRUD Modificada", "modifyFailed": "Falha ao modificar Lista CRUD"}, "listComponent": {"added": "Lista Adicionada", "addFailed": "Falha ao adicionar Lista", "modified": "Lista Modificada", "modifyFailed": "Falha ao modificar Lista"}, "labelComponent": {"added": "Label Adicionado", "addFailed": "Falha ao adicionar Label", "modified": "Label Modificado", "modifyFailed": "Falha ao modificar Label"}, "buttonComponent": {"added": "Botão Adicionado", "addFailed": "Falha ao adicionar <PERSON>", "modified": "Botão Modificado", "modifyFailed": "Falha ao modificar Botão"}, "containerComponent": {"added": "Container <PERSON><PERSON><PERSON><PERSON>", "addFailed": "<PERSON>alha ao adicionar Container", "modified": "Container Modi<PERSON><PERSON>", "modifyFailed": "Falha ao modificar Container"}, "imageComponent": {"added": "Imagem Adici<PERSON>", "addFailed": "Falha ao adicionar Imagem", "modified": "Imagem Modificada", "modifyFailed": "Falha ao modificar Imagem"}, "modal": {"added": "<PERSON><PERSON>", "addFailed": "<PERSON>alha ao adiciona<PERSON>", "modified": "Modal Modificado", "modifyFailed": "Falha ao modificar Modal", "removed": "<PERSON><PERSON>mo<PERSON>", "removeFailed": "<PERSON><PERSON><PERSON> ao remover <PERSON>"}, "modalTab": {"removed": "Tab do Modal Removido", "removeFailed": "Falha ao remover Tab do Modal"}, "selectorComponent": {"added": "<PERSON><PERSON><PERSON>", "addFailed": "<PERSON>alha ao adiciona<PERSON>", "modified": "<PERSON><PERSON><PERSON>", "modifyFailed": "<PERSON>alha ao modificar <PERSON>"}, "kanbanComponent": {"added": "<PERSON><PERSON><PERSON>", "addFailed": "Falha ao adicionar <PERSON>", "modified": "<PERSON><PERSON><PERSON>", "modifyFailed": "Falha ao modificar Kanban"}, "inputComponent": {"added": "Input Adicionado", "addFailed": "Falha ao adicionar Input", "modified": "Input Modificado", "modifyFailed": "Falha ao modificar Input"}, "htmlComponent": {"added": "HTML Adicionado", "addFailed": "Falha ao adicionar HTML", "modified": "HTML Modificado", "modifyFailed": "Falha ao modificar HTML"}, "defaultFailure": "<PERSON><PERSON><PERSON>"}}, "DB_ATTRS": {"Number": "Use este atributo para números, exceto CEP, CNPJ, CPF ou telefone. Para esses, utilize o tipo Texto.", "Text": "Atributo para textos em geral. Também use para CEP, CNPJ, CPF ou telefone.", "Date": "Use este atributo para datas.", "SingleChoice": "Crie uma lista estática de opções para selecionar apenas uma delas.", "LinkToDimension": "Conecte esta tabela a outra por meio de um atributo relacionado.", "Formula": "Atributo que realiza cálculos automáticos com base em fórmulas definidas."}, "S3": {"Planos e Preços": "Planos e Preços", "Planos e Preços - 3 colunas": "Planos e Preços - 3 colunas", "Planos e Preços - 5 colunas": "Planos e Preços - 5 colunas", "Fluxo de Processos": "Fluxo de Processos", "Kanban com Cards Superiores": "Kanban com Cards Superiores", "Fluxo de Processos 1": "Fluxo de Processos 1", "Kanban com Insights": "Kanban com Insights", "Fluxo de Processos 2": "Fluxo de Processos 2", "Kanban com 2 Cards de Insights": "Kanban com 2 Cards de Insights", "Fluxo de Processos 3": "Fluxo de Processos 3", "Análise de Dados": "<PERSON><PERSON><PERSON><PERSON>", "Análise de Dados 1": "Análise de Dados 1", "Dashboard com 4 Cards e 4 Quadrantes": "Dashboard com 4 Cards e 4 Quadrantes", "Análise de Dados 2": "Análise de Dados 2", "Dashboard simples - 2 quadrantes": "Dashboard simples - 2 quadrantes", "Análise de Dados 3": "Análise de Dados 3", "Dashboard 4 Quadrantes": "Dashboard 4 Quadrantes", "Análise de Dados 4": "Análise de Dados 4", "Configurações": "Configurações", "crm_short_description": "Crie cenários personalizados de Gestão de Relacionamento com o Cliente (CRM) para impulsionar suas vendas e fidelidade do cliente.", "crm_long_description": "Com este template, você pode criar cenários personalizados de CRM para o seu negócio, otimizando suas estratégias de vendas e aprimorando o relacionamento com seus clientes. Escolha entre uma variedade de opções e parâmetros, como segmentação de clientes, campanhas de marketing, acompanhamento de leads, e muito mais. Combinando esses elementos, você poderá simular diferentes situações e avaliar os resultados previstos. Experimente diferentes abordagens e tome decisões informadas para aprimorar a eficiência das suas operações comerciais e aumentar a satisfação do cliente.", "Comercial": "Comercial", "Gestão de Tarefas": "Gestão de Tarefas", "task_management_short_description": "Agilize suas tarefas e projetos com este template de cenários de Gestão de Tarefas.", "task_management_long_description": "Este template permite que você crie cenários de Gestão de Tarefas sob medida para a sua equipe e seus projetos. Com uma variedade de opções, como definição de prioridades, atribuição de responsabilidades, estimativas de tempo e acompanhamento de progresso, você poderá simular diferentes fluxos de trabalho. Teste diferentes cenários para otimizar a produtividade, identificar gargalos e melhorar o cumprimento de prazos. Com esse modelo, sua equipe estará mais preparada para enfrentar desafios e trabalhar de forma mais eficiente e coordenada.", "Planejamento Orçamentário": "Planejamento Orçamentário", "budget_planning_short_description": "Tome decisões financeiras mais inteligentes com este template de cenários de Planejamento Orçamentário.", "budget_planning_long_description": "Com este template, você pode criar cenários de Planejamento Orçamentário para sua empresa, auxiliando na tomada de decisões financeiras mais embasadas. Experimente diferentes projeções de receitas e despesas, simule investimentos, corte de custos ou expansões, e veja como cada cenário impacta a saúde financeira do seu negócio. Ao ter uma visão clara de possíveis situações futuras, você estará mais preparado para enfrentar desafios econômicos e identificar oportunidades de crescimento sustentável. Utilize esse modelo para planejar o futuro da sua empresa com mais confiança e segurança.", "Estratégico": "Estratégico", "Financeiro": "Financeiro", "financial_short_description": "Descubra insights valiosos para o sucesso do seu negócio com este template de cenários de Business Intelligence (BI).", "financial_long_description": "Com este template de cenários de Business Intelligence (BI), você pode explorar e analisar dados para descobrir informações estratégicas para o sucesso do seu negócio. Personalize seus cenários selecionando as fontes de dados relevantes, definindo os KPIs (Indicadores-chave de Desempenho) e escolhendo as métricas para análise. Explore diferentes variáveis e filtros para testar diferentes hipóteses e descobrir insights valiosos. Ao utilizar esse modelo, você poderá tomar decisões mais informadas, identificar tendências, oportunidades e desafios, bem como aprimorar a eficiência das suas operações. Aproveite todo o potencial dos seus dados para impulsionar a inovação e o crescimento do seu negócio com inteligência e estratégia.", "CRM": "CRM", "BI": "BI", "RH": "RH", "Caixa de Entrada": "Caixa de Entrada", "Em Andamento": "Em Andamento", "Concluído": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Análises comerciais": "<PERSON><PERSON><PERSON><PERSON>", "Análises de folha de pagamento": "Análises de folha de pagamento", "Análises financeiras": "Análises finance<PERSON>", "Análises contábeis": "<PERSON><PERSON><PERSON><PERSON>", "Dia": "<PERSON>a", "Produto": "Produ<PERSON>", "Valor de vendas": "<PERSON>or de <PERSON>", "Margem de contribuição": "Margem de contribuição", "Cliente": "Cliente", "Vendedor": "<PERSON><PERSON><PERSON>", "Quantidade de vendas": "Quantidade de vendas", "natives": {"Data Criação": "Data Criação", "Usuário Criação": "Usu<PERSON><PERSON>", "Data Últ": {" Alteração": "Data Últ. Alteração"}, "Usuário Últ": {" Alteração": "Usuário Últ. Alteração"}, "Calendário": "<PERSON><PERSON><PERSON><PERSON>", "Usuários": "Usuários", "Tipo de usuário": "Tipo de usuário", "Tela": "Tela", "Módulo": "<PERSON><PERSON><PERSON><PERSON>", "Dia": "<PERSON>a", "Mês": "<PERSON><PERSON><PERSON>", "Ano": "<PERSON><PERSON>", "Trimestre": "Trimestre", "Semana": "Se<PERSON>", "Dia da Semana": "<PERSON><PERSON>", "Int - Dias no ano até hoje": "Int - Dias no ano até hoje", "Int - Ano Atual": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "Int - Mês Atual": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "Int - Dia Atual": "<PERSON><PERSON> - <PERSON><PERSON>", "Int -  Últ 12 meses": "Int -  Últ 12 meses", "Int - Dias no mês até hoje": "Int - Dias no mês até hoje"}, "Listas Mobile (Mobile)": "Listas Mobile (Mobile)", "Lista Simples (Mobile)": "Lista Simples (Mobile)", "Lista Simples com Imagem (Mobile)": "Lista Simples com Imagem (Mobile)", "Lista Simples com Valor (Mobile)": "Lista Simples com Valor (Mobile)", "Lista Duas Métricas (Mobile)": "Lista Duas Métricas (Mobile)", "Lista Múltiplos Atributos (Mobile)": "Lista Múltip<PERSON> (Mobile)", "Lista Tasks (Mobile)": "Lista Tasks (Mobile)", "Listas": "Listas", "Lista Simples": "Lista Simples", "Lista Simples com Imagem": "Lista Simples com Imagem", "Lista Tasks": "Lista Tasks", "Cadastros": "Cadastros", "Outros": "Outros", "Kanban e Botão": "Kanban e Botão", "Lista Simples e Botão": "Lista Simples e Botão", "Quatro CRUDs + Botões": "Quatro CRUDs + Botões", "Esquerda Fixa + 4 CRUDs": "Esquerda Fixa + 4 CRUDs", "Uma Métrica | Sem Mapa": "Uma Métrica | Sem Mapa", "Uma Métrica | Com Mapa": "Uma Métrica | Com Mapa", "Duas Métricas | Sem Mapa": "Duas Métricas | Sem Mapa", "Duas Métricas | Com Mapa": "Duas Métricas | Com Mapa", "Planos e Preços 3 Faixas": "Planos e Preços 3 Faixas", "Planos e Preços 5 Faixas": "Planos e Preços 5 Faixas", "Formulário": "<PERSON><PERSON><PERSON><PERSON>", "Tela Home": "Tela Home", "Todos": "Todos"}, "LOG_ERROR": {"no_code": "Erro durante a execuçao", "100": "Sem conexão com o banco de dados", "101": "Erro na query", "102": "Nome duplicado de dimensão", "103": "Nome duplicado de cubo", "104": "Dimensões no mesmo galho", "105": "Base caiu durante a execução", "106": "Cadastro({id}) não encontrado", "107": "Cubo({id}) não encontrado", "108": "Chipagem não encontrada para a dimensão principal", "109": "Tempo limite da fila do Mitra Connect"}, "SANKHYA": {"request_license": "Solicitar licença"}, "GREETINGS": {"welcome": "bem-vindo"}, "LANGFLOW": {"plan": {"ADD_TELAS": "Criação de Telas", "ADD_DASHBOARD": "Criação de Telas", "ADD_COMPONENT_BUTTON": "Criação de Botões", "ADD_COMPONENT": "Criação de Componentes", "ADD_FORMS": "Criação de Formulários", "ADD_ACTIONS": "Criação de Ações", "STREAM_OBJECT": "Objeto do <PERSON>", "STANDALONE": "Objeto Independente", "UNKNOWN": "<PERSON><PERSON><PERSON><PERSON>", "executed": "Plano executado", "TABELAS_DB": "<PERSON><PERSON><PERSON>", "VARIAVEIS": "Variáveis", "TELAS": "Telas", "DASHBOARD": "Dashboards", "FORMS": "Formulários", "ACTIONS": "Ações", "COMPONENT": "Componentes", "IMPORT": "Importações", "DBACTIONS": "Ações de Database", "DETAILS_MODAL": "<PERSON><PERSON>", "GENERAL_LIST": "Lista Geral", "GENERAL_KANBAN": "Kanban Geral", "component_screen": "Componentes criados na tela", "component_screen_short": "Na tela", "CUBES_DB": "Datasets", "USE_FILES": "Carregamento de arquivos", "PROJECT_SETTINGS": "Projeto", "action": {"screen_creation": "<PERSON><PERSON><PERSON> tela", "button_creation": "<PERSON><PERSON><PERSON> b<PERSON>", "component_creation": "<PERSON><PERSON><PERSON>", "form_creation": "<PERSON><PERSON><PERSON>", "action_creation": "Criando <PERSON>", "object_creation": "<PERSON><PERSON><PERSON> objeto", "unknown_object": "<PERSON>ando objeto", "ADD": "adicionar", "ALT": "atual<PERSON>r", "RUN_DML": "Scripts que vou executar", "RUN_DDL": "Scripts que vou executar", "DELETE": "deletar", "UPLOAD_PUBLIC_FILE": "Arquivos que vou fazer upload", "UPLOAD_LOADABLE_FILE": "Arquivos que vou fazer upload", "ADD_COMPONENT": "Componentes que vou adicionar", "ADD_TELAS": "Telas que vou adicionar", "ADD_DASHBOARD": "Dashboards que vou adicionar", "ADD_FORMS": "Formulários que vou adicionar", "ADD_ACTIONS": "Ações que vou adicionar", "ADD_DBACTIONS": "Ações de database que vou adicionar", "ADD_MODAL": "Modal de detalhes que vou adicionar", "ADD_TABELAS_DB": "Tabelas que vou adicionar", "ADD_CUBES_DB": "Datasets que vou adicionar", "ADD_VARIAVEIS": "Variáveis que vou adicionar", "ADD_DETAILS_MODAL": "Mo<PERSON>s de detalhes que vou adicionar", "ADD_COMPONENT_GENERAL_LIST": "Lista geral que vou adicionar", "ADD_COMPONENT_GENERAL_KANBAN": "<PERSON><PERSON><PERSON> geral que vou adicionar", "ADD_COMPONENT_CARD_GROUP": "Grupo de cartões que vou adicionar", "ADD_COMPONENT_CARDS_GALLERY": "Galeria de cartões que vou adicionar", "ADD_COMPONENT_BARCHART_NEW": "Gráfico de barras que vou adicionar", "ADD_COMPONENT_PIECHART_NEW": "Gráfico de pizza que vou adicionar", "ADD_COMPONENT_MENU": "Menu que vou adicionar", "ADD_COMPONENT_PRETTY_LIST": "Lista que vou adicionar", "ADD_COMPONENT_DYNAMIC_FORM": "Formulário dinâmico que vou adicionar", "ADD_COMPONENT_BORDA": "<PERSON>rda que vou adicionar", "ADD_COMPONENT_AREACHART_NEW": "Gráfico de área que vou adicionar", "ADD_COMPONENT_MENU_MOBILE": "Menu mobile que vou adicionar", "ADD_COMPONENT_CHECKOUT_CARD": "Cartão de checkout que vou adicionar", "ADD_COMPONENT_GANTT": "Gráfico de Gantt que vou adicionar", "ADD_COMPONENT_CALENDAR": "Calendário que vou adicionar", "ADD_COMPONENT_CHECKLIST": "Lista de verificação que vou adicionar", "ADD_COMPONENT_PROGRESSBAR_NEW": "Barra de progresso que vou adicionar", "ADD_COMPONENT_PROGRESSBAR_LIST": "Lista de barras de progresso que vou adicionar", "ADD_COMPONENT_CARD_SELECTOR": "Se<PERSON>or de cartões que vou adicionar", "ADD_COMPONENT_RICH_TEXT": "Texto rico que vou adicionar", "ADD_COMPONENT_RESUME_CARD": "Cartão de resumo que vou adicionar", "ADD_COMPONENT_ATTACHMENT": "Anexo que vou adicionar", "ADD_COMPONENT_TABS_CONTAINER": "Container de abas que vou adicionar", "ADD_PROJECT_SETTINGS": "Configurações do projeto que vou adicionar", "DELETE_TELAS": "Telas que vou deletar", "DELETE_DASHBOARD": "Dashboards que vou deletar", "DELETE_CUBES_DB": "Datasets que vou deletar", "DELETE_COMPONENT": "Componentes que vou deletar", "DELETE_FORMS": "Formulários que vou deletar", "DELETE_ACTIONS": "Ações que vou deletar", "DELETE_DBACTIONS": "Ações de banco de dados que vou deletar", "DELETE_MODAL": "Modal de detalhes que vou deletar", "DELETE_TABELAS_DB": "Tabelas que vou deletar", "DELETE_VARIAVEIS": "Variáveis que vou deletar", "ALT_TABELAS_DB": "Campos que quero criar dentro de uma tabela", "ALT_DETAILS_MODAL": "Modal de detalhes que vou atualizar", "ALT_CUBES_DB": "Datasets que vou atualizar", "ALT_COMPONENT": "Componentes que vou atualizar", "ALT_TELAS": "Telas que vou atualizar", "ALT_DASHBOARD": "Dashboards que vou atualizar", "ALT_FORMS": "Formulários que vou atualizar", "ALT_ACTIONS": "Ações que vou atualizar", "ALT_DBACTIONS": "Ações de banco de dados que vou atualizar", "ALT_MODAL": "Modal de detalhes que vou atualizar", "ADD_IMPORT": "Importações que vou criar", "USE_FILES_AS_PUBLIC": "Arquivos que vou carregar (público)", "USE_FILES_AS_LOADABLE": "Arquivos que vou carregar (importar)", "ALT_VARIAVEIS": "Variáveis que vou atualizar", "ALT_COMPONENT_GENERAL_LIST": "Lista geral que vou atualizar", "ALT_COMPONENT_GENERAL_KANBAN": "<PERSON><PERSON>ban geral que vou atualizar", "ALT_COMPONENT_CARD_GROUP": "Grupo de cartões que vou atualizar", "ALT_COMPONENT_CARDS_GALLERY": "Galeria de cartões que vou atualizar", "ALT_COMPONENT_BARCHART_NEW": "Gráfico de barras que vou atualizar", "ALT_COMPONENT_PIECHART_NEW": "Gráfico de pizza que vou atualizar", "ALT_COMPONENT_MENU": "Menu que vou atualizar", "ALT_COMPONENT_PRETTY_LIST": "Lista que vou atualizar", "ALT_COMPONENT_DYNAMIC_FORM": "Formulário <PERSON>âmic<PERSON> que vou atualizar", "ALT_COMPONENT_BORDA": "<PERSON>rda que vou atualizar", "ALT_COMPONENT_AREACHART_NEW": "Gráfico de área que vou atualizar", "ALT_COMPONENT_MENU_MOBILE": "Menu mobile que vou atualizar", "ALT_COMPONENT_CHECKOUT_CARD": "Cartão de checkout que vou atualizar", "ALT_COMPONENT_GANTT": "Gráfico de Gantt que vou atualizar", "ALT_COMPONENT_CALENDAR": "Calendário que vou atualizar", "ALT_COMPONENT_CHECKLIST": "Lista de verificação que vou atualizar", "ALT_COMPONENT_PROGRESSBAR_NEW": "Barra de progresso que vou atualizar", "ALT_COMPONENT_PROGRESSBAR_LIST": "Lista de barras de progresso que vou atualizar", "ALT_COMPONENT_CARD_SELECTOR": "<PERSON><PERSON>or de cartões que vou atualizar", "ALT_COMPONENT_RICH_TEXT": "Texto rico que vou atualizar", "ALT_COMPONENT_RESUME_CARD": "Cartão de resumo que vou atualizar", "ALT_COMPONENT_ATTACHMENT": "Anexo que vou atualizar", "ALT_COMPONENT_TABS_CONTAINER": "Container de abas que vou atualizar", "ALT_PROJECT_SETTINGS": "Configurações do projeto que vou atualizar", "alt": {"ADD_COMPONENT_GENERAL_LIST": "Lista geral criada", "ADD_COMPONENT_GENERAL_KANBAN": "<PERSON>n<PERSON> geral criada", "ADD_COMPONENT_CARD_GROUP": "Grupo de cartões criada", "ADD_COMPONENT_CARDS_GALLERY": "Galeria de cartões criada", "ADD_COMPONENT_BARCHART_NEW": "Gráfico de barras criado", "ADD_COMPONENT_PIECHART_NEW": "Gráfico de pizza criado", "ADD_COMPONENT_MENU": "<PERSON><PERSON> criado", "ADD_COMPONENT_PRETTY_LIST": "Lista criada", "ADD_COMPONENT_DYNAMIC_FORM": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>o", "ADD_COMPONENT_BORDA": "<PERSON><PERSON> criada", "ADD_COMPONENT_AREACHART_NEW": "Gráfico de área criado", "ADD_COMPONENT_MENU_MOBILE": "Menu mobile criado", "ADD_COMPONENT_CHECKOUT_CARD": "Cartão de checkout criado", "ADD_COMPONENT_GANTT": "Gráfico de Gantt criado", "ADD_COMPONENT_CALENDAR": "<PERSON><PERSON><PERSON><PERSON>", "ADD_COMPONENT_CHECKLIST": "Lista de verificação criada", "ADD_COMPONENT_PROGRESSBAR_NEW": "Barra de progresso criada", "ADD_COMPONENT_PROGRESSBAR_LIST": "Lista de barras de progresso criada", "ADD_COMPONENT_CARD_SELECTOR": "<PERSON><PERSON><PERSON> de cartões criado", "ADD_COMPONENT_RICH_TEXT": "Texto rico criado", "ADD_COMPONENT_RESUME_CARD": "Cartão de resumo criado", "ADD_ATTACHMENT": "Anexo criado", "ADD_COMPONENT_TABS_CONTAINER": "Container de abas criado", "ADD_PROJECT_SETTINGS": "Configurações do projeto criadas", "ALT_COMPONENT_GENERAL_LIST": "Lista geral atualizada", "ALT_COMPONENT_GENERAL_KANBAN": "Kanban geral atualizada", "ALT_COMPONENT_CARD_GROUP": "Grupo de cartões atualizada", "ALT_COMPONENT_CARDS_GALLERY": "Galeria de cartões atualizada", "ALT_COMPONENT_BARCHART_NEW": "Gráfico de barras atualizado", "ALT_COMPONENT_PIECHART_NEW": "Gráfico de pizza atualizado", "ALT_COMPONENT_MENU": "<PERSON><PERSON>", "ALT_COMPONENT_PRETTY_LIST": "Lista atualizada", "ALT_COMPONENT_DYNAMIC_FORM": "<PERSON>ul<PERSON><PERSON>mico atualizado", "ALT_COMPONENT_BORDA": "Borda atualizada", "ALT_COMPONENT_AREACHART_NEW": "Gráfico de área atualizado", "ALT_COMPONENT_MENU_MOBILE": "Menu mobile atualizado", "ALT_COMPONENT_CHECKOUT_CARD": "Cartão de checkout atualizado", "ALT_COMPONENT_GANTT": "Gráfico de Gantt atualizado", "ALT_COMPONENT_CALENDAR": "<PERSON><PERSON><PERSON><PERSON>", "ALT_COMPONENT_CHECKLIST": "Lista de verificação atualizada", "ALT_COMPONENT_PROGRESSBAR_NEW": "Barra de progresso atualizada", "ALT_COMPONENT_PROGRESSBAR_LIST": "Lista de barras de progresso atualizada", "ALT_COMPONENT_CARD_SELECTOR": "<PERSON><PERSON>or de cartões atualizado", "ALT_COMPONENT_RICH_TEXT": "Texto rico atualizado", "ALT_COMPONENT_RESUME_CARD": "Cartão de resumo atualizado", "ALT_ATTACHMENT": "Anexo atualizado", "ALT_COMPONENT_TABS_CONTAINER": "Container de abas atualizado", "ALT_PROJECT_SETTINGS": "Configurações do projeto atualizadas", "ALT_COMPONENT": "Componentes alterados", "ALT_TELAS": "Telas alteradas", "ALT_DASHBOARD": "Dashboards alteradas", "ALT_ACTIONS": "Ações alteradas", "ALT_DBACTIONS": "Ações de database alteradas", "ALT_MODAL": "Modal de detalhes alterado", "ALT_TABELAS_DB": "Tabelas alteradas", "ALT_VARIAVEIS": "Variáveis alteradas", "ALT_CUBES_DB": "Datasets alterados", "DELETE_COMPONENT": "Componentes deletados", "DELETE_TELAS": "Telas deletadas", "DELETE_DASHBOARD": "Dashboards deletadas", "DELETE_FORMS": "Formulários deletados", "DELETE_ACTIONS": "Ações <PERSON>das", "DELETE_DBACTIONS": "Ações de database deletadas", "DELETE_MODAL": "Modal de de<PERSON>hes del<PERSON>do", "DELETE_CUBES_DB": "Datasets deletados", "DELETE_TABELAS_DB": "Tabelas deletadas", "DELETE_VARIAVEIS": "Variáveis <PERSON>", "ADD_IMPORT": "Importações criadas", "USE_FILES_AS_PUBLIC": "<PERSON><PERSON><PERSON><PERSON> car<PERSON> (público)", "USE_FILES_AS_LOADABLE": "<PERSON><PERSON><PERSON><PERSON> carregados (importar)", "ADD_COMPONENT": "Componentes criados", "ADD_TELAS": "Telas criadas", "ADD_DASHBOARD": "Dashboards criadas", "ADD_FORMS": "Formulários criad<PERSON>", "ADD_ACTIONS": "<PERSON>ç<PERSON><PERSON> c<PERSON>", "ADD_DBACTIONS": "Ações de database criadas", "ADD_MODAL": "<PERSON><PERSON> de de<PERSON>hes criado", "ADD_TABELAS_DB": "<PERSON><PERSON><PERSON> criadas", "ADD_CUBES_DB": "Datasets criados", "ADD_VARIAVEIS": "Variáveis criad<PERSON>", "RUN_DML": "Scripts executados", "ALT_FORMS": "Formulários alterados", "ADD_DETAILS_MODAL": "<PERSON><PERSON><PERSON> <PERSON><PERSON> criados", "ADD_ATRIBUTES_DB": "Campos criados dentro de uma tabela", "DELETE_ATRIBUTES_DB": "Campos deletados dentro de uma tabela", "ALT_ATRIBUTES_DB": "Campos alterados dentro de uma tabela", "ALT_DETAILS_MODAL": "Modais de detalhes alterados", "RUN_DDL": "Scripts executados"}}, "DML": "Operação DML", "DDL": "Operação DDL"}}, "BUTTONS": {"cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "next": "Próximo", "start": "Iniciar", "complete": "Concluir", "update": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "back": "Voltar", "replace": "Substituir", "remove": "Remover", "access": "Acessar", "set_up": "Configurar", "confirm": "Confirmar"}, "TIPS": {"request_access": "Entre em contato com o responsável para solicitar.", "NO_SCREEN": {"title": "Tela inicial não configurada", "subtitle": "Entre em contato com o responsável para configurar a tela inicial"}, "NO_ACCESS": {"no_has_access": "Ops, você não tem acesso", "maintenance_project": "Projeto em manutenção", "try_latter": "Tente acessar novamente em breve."}}, "NO_SPACES": {"title": "Ops! Você ainda não está em nenhum workspace.", "email_access": "Você acessou com o email", "not_right": "Não está certo?", "use_another_email": "Entrar com outro email", "already_client": "Já sou cliente Mitra", "request_access": "Entre em contato com o administrador do workspace para solicitar acesso.", "beta_tester": "Sou beta tester da nova versão", "suport_to_register": "Se você já foi selecionado como beta tester, favor entrar em contato conosco que vamos ajustar seu cadastro.", "try_new_version": "Quero testar a nova versão do Mitra", "wait_list_tip": "Entre na lista de espera para receber acesso antecipado!", "wait_list_enter": "Entrar na lista de espera", "email_added": "<PERSON>ail adicionado"}, "LOGGED": {"manage_account": "Gerenciar conta", "current_workspace": "Workspace atual", "change_workspace": "Trocar de workspace", "new_workspace": "Novo workspace", "portuguese": "Português ", "portuguese_brazil": "Portuguê<PERSON>", "english": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "FORMS": {"new_detail_modal": "Novo modal de detalhes", "new_form": "Novo formulário", "required": "Esse campo é obrigatório", "fill_required_fields": "Preencha os campos obrigatórios!", "duplicate_name": "Nome Duplicado", "select_field_below": "Selecione um campo abaixo", "fill_form": "Preencha o formulário a seguir com suas informações. Em breve nossa equipe de vendas entrará em contato com você.", "talk_to_us": "Fale conosco"}, "ACCOUNT": {"personal_information": "Informações pessoais", "update_name_and_photo_here": "Atualize seu nome e fotos aqui.", "your_photo": "Sua foto", "photo_appears_profile": "A foto que aparece no seu perfil.", "password_update": "Primeiro insira sua senha atual para atualizar.", "current_password": "<PERSON><PERSON> atual", "enter_current_password": "Insira sua senha atual", "new_password": "Nova senha", "enter_new_password": "Insira sua nova senha", "confirm_new_password": "Confirmar nova senha", "re_enter_new_password": "Insira novamente sua nova senha", "language": "Idioma", "language_config_body": "Selecione o idioma da aplicação. As fórmulas da planilha também mudam de acordo com o idioma.", "logout": "<PERSON><PERSON> da conta", "field_cannot_empty": "Esse campo não pode ser vazio.", "click_to_select": "Clique para selecionar", "or_drag_and_drop_image": "ou arraste e solte a imagem aqui."}, "LOGIN": {"user_not_found": "Usuário não encontrado", "invalid_email_password": "E-mail ou senha in<PERSON>", "error": "Falha ao fazer login", "login_title": "Acesse sua conta", "create_account": "C<PERSON><PERSON> conta", "login_subTitle": "Bem-vindo! Preencha suas informações.", "insert_email": "Digite seu e-mail", "insert_password": "Digite sua senha", "forgot_password": "<PERSON><PERSON><PERSON> a senha", "please_insert_email": "Por favor, digite seu e-mail!", "please_insert_password": "Por favor, digite sua senha!", "please_insert_name": "Por favor, digite seu nome!", "account_login": "Entrar com conta", "not_have_account": "Ainda não tem uma conta?", "create_account_here": "Crie a sua aqui.", "create_mitra_account": "Crie sua conta {name}", "sign_up": "Registre-se", "sign_up_msg": "<PERSON><PERSON><PERSON> as informações abaixo para começar", "enter_name": "Digite seu nome", "invalid_email": "E-mail inválido", "at_least_6_characters": "Pelo menos 6 caracteres", "password_must_contain": "<PERSON>a senha deve conter:", "at_least_6_characters_2": "No mínimo 6 caracteres", "uppercase_and_lowercase": "Letras maiúsculas e minúsculas", "numbers": "Números", "already_have_account": "Já possui conta?", "login": "Fazer login", "check_email": "Confira seu e-mail", "sended_code_to": "Enviamos um código de verificação para o e-mail", "didnt_receive_code": "Não recebeu o código?", "wait": "<PERSON><PERSON><PERSON>", "resend": "para reenviar", "click_resend": "Clique para reenviar", "back_registration_screen": "Voltar para a tela de cadastro", "back_login_screen": "Voltar para a tela de login", "verification_completed": "Verificação concluída!", "redirecting_your_first": "Estamos redirecionando você para seu primeiro Workspace!", "forgot_password_2": "Esque<PERSON>u a senha?", "verify": "Verificar", "reset_password": "Redefina sua senha", "reset": "Redefinir", "input_password_again": "Digite sua senha novamente", "please_confirm_password": "Por favor, confirme a senha!", "password_reset_description": "Sua senha deve ser diferente das senhas utilizadas anteriormente."}, "PROJECT": {"remove_header_warning_html": "Ao remover o cabeçalho padrão da plataforma, o projeto será exibido {bold1}sem menu de navegação{bold2} ou {bold1}botão de logout{bold2}.\nEssa opção é recomendada apenas para desenvolvedores que irão configurar seu próprio menu personalizado.\n\nVocê pode reverter essa opção a qualquer momento nas configurações do projeto, na aba Web.", "remove_native_header": "Remover cabeçalho nativo", "remove_header": "Remover cabeçalho", "json_config": "Configurar JSON", "preparing_project": "Preparando seu projeto...", "first_project": "<PERSON><PERSON> primeiro projeto", "new_html_screen": "Nova tela HTML", "html_screen": "Tela HTML", "delete_dashboard": "Deletar Dashboard", "not_verified": "Não verificado", "verified": "Verificado", "request_verification": "Solicitar verificação", "send_email_action": "E-mail para ação ”Enviar E-mail”", "recover_password_email": "E-mail de recuperar senha", "create_account_email": "E-mail de criar conta", "invite_email_body": "Você pode usar HTML para configurar o texto. \nVariáveis Obrigatórias: $codigo (Retorna o código de verificação pra recuperar a senha) \nVariáveis Disponíveis: $emailUsuario (Retorna o e-mail do usuário convidado)", "invite_email_body_hint": "Você pode usar HTML para configurar o texto. \nVariáveis Obrigatórias: $link (Retorna o link de convite do projeto) \nVariáveis Disponíveis: $emailUsuario (Retorna o e-mail do usuário convidado) - $projeto (Retorna o nome do Projeto)", "only_verified_emails": "Somente e-mails verificados podem ser usados.", "email_body": "Corpo do e-mail", "title_email": "Título do E-mail", "sender": "Remetente", "invite_email": "E-mail de convite", "register_email": "Cadastrar novo E-mail", "email_registration": "Cadastro de e-mails", "email_registration_preview": "Pré-visualizar", "email_registration_edit": "<PERSON><PERSON>", "project_type": "Tipo do projeto", "home_screen": "Tela Inicial da solução *", "configure_project": "Configurar projeto", "delete_sure": "Tem certeza? Todos os dados serão deletados...", "cancel_subscription": "Cancelar assinatura", "native_screens": "Telas nativas", "publish_screen": "Publicar tela", "unpublish_screen": "Despublicar tela", "no_published_screens": "Nenhuma tela publicada", "import_spreadsheet": "Importar Planilha", "project_data": "Dados do projeto", "project_name": "Nome do projeto", "insert_project_name": "Insira o nome do projeto", "this_field_is_mandatory": "Esse campo é obrigatório.", "empty_project_text_1": "Você ainda não tem nenhuma planilha ou pasta,", "empty_project_text_2": "comece a criar!", "import_file": "Importar planilha", "click_to_select": "Clique para selecionar", "or_drag_and_drop": "ou arraste e solte o arquivo aqui. | ou arraste e solte o arquivo", "quick_access": "<PERSON><PERSON>", "create_screen": "C<PERSON>r Tela", "create_dashboard": "Criar Dashboard", "create_new_blank_screen": "Criar nova tela em branco", "create_mobile_screen": "Criar Tela Mobile", "create_document": "<PERSON><PERSON>r <PERSON>", "create_new_blank_mobile_screen": "Criar nova tela mobile em branco", "create_new_blank_dashboard": "Criar novo dashboard em branco", "published": "Publicada", "unpublished": "Não publicada", "show_unpublished": "Mostrar não publicadas", "show_unpublished_screens": "Mostrar telas não publicadas", "hide_unpublished_screens": "Ocultar telas não publicadas", "new_screen": "Nova tela", "new_document": "Novo Documento", "duplicate_screen": "Duplicar tela", "duplicate_doc": "Duplicar documento", "where_create_screen": "Onde você gostaria de criar a nova tela?", "where_create_dashboard": "Onde você gostaria de criar o novo dashboard?", "new_mobile_screen": "Nova tela mobile", "duplicate_mobile_screen": "Duplicar tela mobile", "where_create_mobile_screen": "Onde você gostaria de criar a nova tela mobile?", "screen": "Tela", "mobile_screen": "Tela mobile", "spreadsheets": "<PERSON>il<PERSON>", "new_folder": "Pasta", "insert_name": "Inserir nome", "create_folder": "<PERSON><PERSON><PERSON>", "create_new_folder": "Criar uma nova pasta", "create_new_project": "Criar novo projeto", "create_new_project_dev": "Criar Novo Projeto", "delete_folder": "Deletar pasta?", "error_deleting_folder": "Erro ao deletar pasta", "error_deleting_screen": "Erro ao deletar tela", "delete_folder_body_1": "Tem certeza que deseja deletar a pasta", "delete_folder_body_2": "Essa ação não pode ser desfeita e todos os dados referentes a pasta serão deletados!", "delete_screen": "Deletar tela?", "delete_doc": "Deletar documento?", "delete_screen_body_1": "Tem certeza que deseja deletar a tela", "delete_dashboard_body_1": "Tem certeza que deseja deletar o dashboard", "delete_doc_body_1": "Tem certeza que deseja deletar o documento", "delete_screen_body_2": "Essa ação não pode ser desfeita e todos os dados referentes a tela serão deletados!", "delete_doc_body_2": "Essa ação não pode ser desfeita e todos os dados referentes ao documento serão deletados!", "do_not_have_modules_yet": "Você ainda não tem nenhum modulo, comece a criar!", "do_not_have_screen_yet": "Você ainda não tem nenhuma tela, comece a criar!", "no_access": "Você não possui acesso a esse workspace", "no_project_selected": "Nenhum projeto selecionado", "new_dashboard": "Novo Dashboard", "choose_model": "Escolha um modelo", "choose_template": "Escolha um <PERSON>", "white_screen": "Tela em branco", "white_list": "Lista em branco", "create_screen_from_scratch": "<PERSON>rie uma tela do zero", "enable_freedb": "Utilizar banco de analytics", "TEMPLATE": {"use_demo_template": "Usar demo", "tweet_template_description": "<PERSON><PERSON><PERSON> título, texto do tweet, suporte para imagens/vídeos, estado vazio e interações como hover e selecionado.", "checkbox_template_description": "<PERSON><PERSON><PERSON> t<PERSON>, texto de suporte e estados padrão, selecionado e hover.", "select_template": "Selecione um template", "template_gallery": "Galeria de Templates", "use_template": "<PERSON><PERSON>", "start_from_scratch": "Iniciar do <PERSON>", "search_icons": "Buscar ícones", "create_scratch": "Criar projeto do zero", "project_type": "Tipo do projeto", "other_type": "Outro tipo", "other_type_name": "Nome do outro tipo", "process_flow": "Fluxo de Processo", "other": "Outro", "start_pipe": "Vamos começar personalizando seu pipe!", "pipe_steps": "Etapas do Pipe", "pipe_steps_text": "<PERSON>oc<PERSON> pode editar as j<PERSON> existentes, apagar ou adicionar novas.", "card_area": "Campos do Card", "card_area_text": "Você pode editar os já existentes, apagar ou adicionar novos.", "new_step": "Nova etapa"}, "duplicate_project": "Duplicar projeto", "copy_of": "Cópia de", "must_be_creator_in_workspace": "É necessário ser criador em um Workspace para realizar essa ação.", "send_to_qa": "Enviar para QA", "successfully_duplicated_qa_base": "Duplicado com sucesso na base de QA", "load_users": "<PERSON><PERSON><PERSON>", "error": "erro | erros", "to_correct": "para corrigir", "search_for_errors": "Buscar erros", "no_errors_found": "Nenhum erro encontrado.", "last_run": "Ultima execução", "to_update": "<PERSON><PERSON><PERSON><PERSON>", "looking_for_errors": "Buscando erros...", "in_few_moments_your_action_will_completed": "Em alguns instantes sua ação será concluída.", "existing_stores": "Selecionar templates", "folder_name": "Criar novo template", "insert_base_name": "Insira o nome da base", "there_already_base_with_that_name": "Já existe um projeto com esse nome", "copie_data_to_json_model": "Copie os dados abaixo para o modelo json:", "template_creation": "Criação do template: ", "template_update": "Atualização do template: ", "sure_want_to_update": "Tem certeza que deseja atualizar?", "sure_want_to_create": "Tem certeza que deseja criar?", "enable_mobile_version_for_project": "Ativar versão mobile para este projeto", "project_appears_on_mobile": "O projeto aparece na lista de projetos dentro do mobile", "has_home_screen": "Possui tela inicial", "if_dont_you_can_access_any_published_screen": "<PERSON><PERSON>o não possua, você poderá acessar qualquer uma das telas publicadas", "solution_home_screen": "Escolha a tela inicial", "choose_home_screen": "Escolha a tela inicial", "screen_opens_by_default_when_user_enters_the_app": "Esta é a tela que abre por padrão quando o usuário entra na aplicação", "screen_opens_by_default_when_user_enters_the_web": "Esta é a tela que abre por padrão quando o usuário entra na aplicação", "manage_and_delete_api_keys": "Gerencie e exclua chaves de API", "integrate_mitra_with_any_other_system_api": "Integre o {system} com qualquer outro sistema via API", "key_name": "Nome da chave", "private_key": "Chave privada", "create_new_api_key": "Criar nova chave de API", "you_do_not_have_any_api_key": "Você ainda não possui nenhuma chave de API criada até o momento.", "choose_tables_will_be_released_for_api": "<PERSON><PERSON>l<PERSON> as tabelas que serão liberadas para conexão através da API.", "selected_tables": "<PERSON><PERSON>", "copy_url": "Copiar URL", "copy_example": "Copiar Exemplo", "delete_key": "Deletar chave", "create_api_key": "Criar chave API", "enter_name_for_your_api_key": "Insira um nome para sua chave API", "basic_information": "Informações Básicas", "release_for_end_user": "Liberações para o usuário final", "home_screen_solution": "Tela Inicial da solução*", "home_screen_information": "Esta é a tela que abre por padrão quando o usuário entra na aplicação.", "dynamic_home_screen": "Tela inicial dinâmica por perfil", "dynamic_home_screen_information": "Permite que seja definido uma tela inicial para cada perfil existente.", "no_profile_registered": "Nenhum perfil cadastrado", "profile": "Perfil", "duplicate_dashboard": "Duplicar Dashboard", "home_screen_header": "Tela inicial", "shared_with_me": "Compartilhados comigo", "playground_info": "Permite que os usuários acessem o Playground (Self Service BI)", "allows_users_create_screens": "Permite que os usuários criem dashboards e telas a partir de comandos da Lila", "allow_dashboard_users": "Permitir c<PERSON>ção de dashboards para usuários", "lila_home_page": "Lila como página inicial", "lila_info": "Permite que seus usuários finais utilizem o copilot dentro do modo preview", "ai_info": "Permite que os usuários acessem a {name} (nossa IA)", "lila_home_page_info": "Define a Lila como página inicial do seu projeto.", "allow_lila_to_execute_actions": "<PERSON><PERSON><PERSON> que a <PERSON> execute ações no seu projeto", "profile_tools_info": "Você deve preencher os perfis dos seus usuários e declarar quais serão as `tools` (ações e tabelas) que serão liberadas para cada perfil.", "view_tools_schema": "Visualizar schema das tools", "teach_ai_to_work_with_tools": "Ensine a IA a trabalhar com as tools, utilizando linguagem natural.", "guidelines_for_ai": "Diretrizes para a IA", "write_guidelines": "<PERSON><PERSON><PERSON><PERSON> as diretrizes", "choose_connections": "<PERSON><PERSON><PERSON><PERSON> as conexões que ficarão disponíveis", "allow_to_import_csv_sql": "Permite que os usuários importem os dados por CSV ou SQL.", "screen_plans_and_prices": "Tela de planos e preços", "screen_plans_and_prices_info": "Escolha a tela de planos e preços", "screen_show_attribute": "Esta tela irá aparecer caso o atributo de bloqueio esteja ativo.", "lock_attribute": "Atributo de bloqueio", "choose_blocking_attribute": "Escolha o atributo de bloqueio", "choose_blocking_attribute_info": "Mostra a tela de bloqueio de acordo com o valor definido no cadastro.", "delete_project_warning": "Apagar seu projeto é uma ação irreversível que resultará na exclusão completa de seus projetos e dados, sem possibilidade de recuperação.", "create_dashboard_with": "Criar Dashboard com a {name}", "screens": "Telas", "registrations": "Cadastros", "locks": "Bloqueios", "privacy": "Privacidade do Projeto", "privacy_hint": "Defina quem pode acessar sua aplicação: {p1} permite acesso livre por link; {p2} exige autenticação; {p3} restringe o acesso apenas a usuários autorizados e logados.", "public": "Público sem login", "public_with_login": "Público com login", "private": "Privado", "chose_lock_screen": "Escolha a tela de bloqueio", "lock_screen_hint": "Esta tela irá aparecer de acordo com o atributo de bloqueio.", "organize_screens_by_sessions": "Organizar telas por seções no menu", "show_menu": "<PERSON><PERSON>", "show_menu_tip": "Menu nativo do sistema, onde aparecem as telas publicadas", "this_option_divides_the_preview_mode_navigation": "Essa opção divide o menu de navegação do usuário final por seções, baseadas na estrutura de pastas do projeto.", "project_frozen": "<PERSON><PERSON><PERSON>", "project_frozen_info": "Este projeto está congelado devido à inatividade nos últimos 30 dias.", "click_here_to_restore": "Clique aqui para restaurar seu projeto.", "general": "G<PERSON>", "advanced": "Avançado", "integrations": "Integrações", "mobile_publishing": "Publicação Mobile", "mitra_apps_header": "<PERSON><PERSON> - <PERSON><PERSON>", "mitra_apps_info": "O Mitra Apps é uma plataforma mobile do Mitra onde você pode testar seu aplicativo com a comunidade. Ao publicar seu projeto, um PIN exclusivo é gerado, permitindo acesso à área de testes no Mitra Apps. Com o PIN, você poderá testar seu app diretamente no app mobile, garantindo que ele esteja funcionando conforme esperado antes de submetê-lo ao catálogo público.", "publish_mitra_apps": "Publicação na Apple Store e Play Store", "publish_mitra_apps_info": "Para disponibilizar seu app em plataformas Android e iOS, acesse nosso repositório do GitHub e siga a documentação.", "github_repository": "Repositório <PERSON>", "publication_key": "Chave de publicação", "lila_for_final_users": "{name} para usuários finais", "additional_considerations": "Considerações Adicionais", "add_considerations": "Adicionar <PERSON>", "cosideration_details_1": "Use este campo para adicionar informações e considerações que serão concatenadas com os prompts dos usuários finais. Essas diretrizes podem ajudar a", "cosideration_details_2": "a fornecer respostas mais específicas e relevantes com base nas instruções fornecidas.", "data_for_ai": "Dados para IA", "lila_for_end_users": "{name} para usuários finais", "change_image": "Alterar Imagem", "login_using_sso": "Login usando SSO", "allow_login_with_google_or_microsoft": "Permite que seus usuários efetuem login com Google ou Microsoft", "create_account": "<PERSON><PERSON><PERSON>", "allow_users_create_accounts": "Permite que usuários criem suas contas sem convite", "total_users": "Total de usuários", "developers_users": "Usuários desenvolvedores", "business_users": "Usuários Business", "lila_credits": "<PERSON><PERSON><PERSON><PERSON> (IA)", "credit_balance": "<PERSON>do <PERSON>", "add_credits": "<PERSON><PERSON><PERSON><PERSON>", "recharge_history": "Histórico de recargas", "transaction_history": "Histórico de transações", "confirm_payment": "Confirma<PERSON> paga<PERSON>", "choose_value": "Escolha um valor", "other_value": "Outro valor", "enter_cpf_cnpj": "Digite CPF ou CNPJ", "card_details": "Dados do cartão", "project": "Projeto", "project_not_have_prepared_data_yet": "Seu projeto ainda não tem dados preparados", "prepare_data": "Preparar dados", "select_tables": "Selecionar Tabelas", "select_table_that_you_want": "<PERSON><PERSON><PERSON><PERSON> as tabelas que a", "will_have_access": "<PERSON><PERSON><PERSON>:", "publish_your_project": "Publique seu projeto", "publish_project_body": "Publique seu projeto em poucos segundos. Simples, rápido e você pode despublicar quando precisar.", "mysite": "meusite", "code_mitra_apps_development_mode": "Use este código no Mitra Apps para acessar o modo de desenvolvimento, onde você poderá testar o aplicativo mobile antes de publicá-lo.", "standard_mode": "<PERSON><PERSON>", "set_default_mode_for_lila": "Define o modo padrão para a Lila ao iniciar um novo chat", "analyze_components_from_other_screens": "Analisar componentes de outras telas", "defines_whether_components_from_other_screens": "Define se os componentes de outras telas vão ser analisados no contexto por padrão", "lila_ai": "Lila (IA)", "content_copied_clipboard": "Conteúdo copiado para a área de transferência", "error_copying_content": "Erro ao copiar conte<PERSON>do"}, "SIGNUP": {"error": "Falha ao cadastrar", "passwords_not_the_same": "As senhas não estão iguais."}, "AUTH": {"invalid_code": "Código inválido!", "code_validation_error": "Falha ao validar código", "reset_password_error": "<PERSON><PERSON>ha ao resetar senha"}, "GLOBAL": {"no_icon_found": "<PERSON><PERSON>hum <PERSON> encontrado para", "do_upgrade": "Upgrade", "loading": "Carregando...", "expand": "Expandir", "COMPONENT": {"input_attach": "Input de anexo", "input_button": "Input de botão", "react": "React", "table": "<PERSON><PERSON><PERSON>", "graph": "Gráfico", "crud_list": "Lista CRUD", "label": "Label", "button": "Botão", "container": "Container", "imagem": "Imagem", "image": "Imagem", "kanban": "Ka<PERSON><PERSON>", "react_list": "Lista React", "input": "Input", "html": "HTML", "template": "Template", "general": "G<PERSON>", "general_list": "Lista Geral", "selector": "<PERSON><PERSON><PERSON>", "list": "Lista", "generic": "Componente", "cardboard": "Cardboard", "input_text": "Input", "input_dropdown": "Input dropdown", "barchart": "Gráfico de barras", "piechart": "Gráfico de pizza", "input_number": "Input numérico", "input_date": "Input de data", "input_textarea": "Input de texto", "menu": "<PERSON><PERSON>", "card_group": "Cards", "barchart_new": "Gráfico de barras", "piechart_new": "Gráfico de pizza", "general_list_qa": "<PERSON><PERSON><PERSON>"}, "how_works": "Como funciona", "grammar": {"a": "a", "o": "o", "the": "o", "and": "e", "to": "para", "with": "com", "for": "para", "on": "em", "by": "por", "from": "de", "of_the": "do", "of_a": "de um", "of_an": "de um", "of_the_a": "de uma", "of_the_an": "de uma", "of_the_the": "do", "of_the_the_a": "do", "of_the_the_an": "do", "of_the_a_the": "de uma", "of_the_a_the_a": "de uma", "of_the_a_the_an": "de uma"}, "credits": "C<PERSON>dit<PERSON>", "connect": "Conectar", "that_i_will": "que vou", "unknown": "Desconhecido", "setup_key": "Configurar chave", "setup_key_of": "Configurar chave da {name}", "name_of_workspace": "Nome do Workspace", "video": "Vídeo", "template": "Template", "action": "Ação", "detail": "<PERSON><PERSON>", "open_preview": "Abrir em modo Preview", "open_app": "Abrir projeto publicado", "init": "<PERSON><PERSON>o", "playground_access": "Acesso ao Playground", "type": "Tipo", "soon": "Em breve", "community": "Comunidade", "phone": "Telefone", "developer": "<PERSON><PERSON><PERSON><PERSON>", "corporate": "Corporativo", "image": "Imagem", "or": "ou", "general": "G<PERSON>", "select_all": "<PERSON><PERSON> to<PERSON>", "see_logs": "Ver logs", "copy_link": "Copiar link", "copied_link": "<PERSON> copiado", "copied": "Copiado", "click_to_copy": "Clique para copiar", "import": "Importar", "origin": "Origem", "name": "Nome", "month": "mês", "includes": "Inclui", "first_vowel": "O", "options": "Opções", "search2": "Buscar", "look_for": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON><PERSON>", "edit_appearance": "Editar <PERSON>", "404_message": "Página não encontrada", "delete_alert": "This action cannot be undone. Are you sure you want to delete?", "actions": {"continue": "<PERSON><PERSON><PERSON><PERSON>", "back": "Voltar", "apply": "Aplicar", "delete": "Deletar", "filter": "Filtrar", "create": "<PERSON><PERSON><PERSON>", "send": "Enviar", "reset": "Redefinir", "update": "Alterar", "edit": "<PERSON><PERSON>", "remove": "Remover"}, "ERRORS": {"general": "Ops! Algo deu errado, recarregue a página", "try_again": "Ops! Algo deu errado, tente novamente", "rename_error": "Falha ao renomear {name}", "load_error": "Falha ao carregar {name}", "ops_some_error": "Ops! Algo deu errado", "reload_page": "Tente recarregar a página", "not_found": "Ops, página não encontrada!", "verify_address": "Verifique se o endereço está correto."}, "form": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmar", "new_project": "Novo Projeto", "new_project_without_prompt": "Novo projeto sem prompt", "projects": "Projetos", "search_projects": "Buscar projetos...", "search_workspace": "Buscar workspace...", "open": "Abrir", "rename": "Renomear", "duplicate": "Duplicar", "move": "Mover", "spreadsheet": "<PERSON><PERSON><PERSON>", "folder": "Pasta", "edit": "<PERSON><PERSON>", "share": "Compartilhar", "check_all": "<PERSON><PERSON>", "linked_tags": "Tags vinculadas", "link_tags": "Vincular tags", "knowledge_base": "Base de Conhecimento", "make_knowledge_base": "Promover {name} como Base de Conhecimento", "make_knowledge_base_message": "Ao promover este projeto como Base de Conhecimento, o atual deixará de ser a base principal e os projetos vinculados perderão suas conexões com ele.", "remove_knowledge_base": "Remover {name} da Base de Conhecimento", "remove_knowledge_base_message": "Ao remover este projeto como Base de Conhecimento, todos os projetos atualmente vinculados perderão o acesso às suas tags e associações. Essa ação é irreversível.", "remove_knowledge_base_title": "Remover Base de Conhecimento", "make_public": "<PERSON><PERSON>", "make_private": "<PERSON><PERSON> privado", "public_chat": "<PERSON><PERSON>", "delete": "Deletar", "new": "Novo", "email": "E-mail", "password": "<PERSON><PERSON>", "permission": "<PERSON><PERSON><PERSON><PERSON>", "invite": "<PERSON><PERSON><PERSON>", "log_out": "<PERSON><PERSON>", "start": "<PERSON><PERSON><PERSON>", "insert": "Inserir", "close": "<PERSON><PERSON><PERSON>", "settings": "Configurações", "data": "<PERSON><PERSON>", "correct": "<PERSON><PERSON><PERSON><PERSON>", "all": "Todos", "select": "Selecionar", "native": "Nativa", "cube": "<PERSON><PERSON><PERSON>", "dimension": "Cadastro", "table": "<PERSON><PERSON><PERSON>", "shortly": "Em breve", "no_results_found": "Nenhum resultado encontrado.", "create_new": "Criar novo | Criar nova", "make_store": "Enviar para a store", "search_2": "Busca", "copy": "Copiar", "required": "Obrigatório", "default": "Padrão", "example": "Exemplo", "attributes": "Atributos", "arguments": "Argumentos", "of": "de", "previous": "Anterior", "next": "Próximo", "select_files": "Selecionar arquivos", "restore": "Restaurar", "history": "Hist<PERSON><PERSON><PERSON>", "current_version": "<PERSON><PERSON><PERSON>", "finish": "Finalizar"}, "CREATE_COLUMN_MODAL": {"text": "Use para textos longos ou de tamanho indefinido, como descrições, coment<PERSON>rios ou conteúdos extensos.", "varchar": "Ideal para textos curtos com tamanho definido. O limite padrão é 256 caracteres, mas pode ser ajustado.", "extra_options": "Opções extras", "field_size": "Tamanho do campo", "edit_column_title": "<PERSON><PERSON> coluna", "create_new_column_title": "Criar nova coluna", "labels": {"name": "Nome", "type": "Tipo", "default_value": "<PERSON>or <PERSON>"}, "placeholders": {"column_name": "Inserir nome da coluna", "select": "Selecionar", "null": "NULL"}, "foreign_key_section_title": "Conexão com outra tabela (Foreign key)", "relation_with": "Relação com:", "edit_connection": "<PERSON><PERSON>", "add_connection": "Adicionar <PERSON> com outra tabela", "constraints_section_title": "Restrições", "constraints": {"primary": {"label": "Primária", "description": "Indica que uma coluna, ou um grupo de colunas, pode ser usada como identificador único para as linhas da tabela."}, "unique": {"label": "Única", "description": "<PERSON>aran<PERSON> que os valores na coluna sejam únicos entre todas as linhas da tabela."}, "required": {"label": "Obrigatória", "description": "Exige que a coluna tenha sempre um valor. Não permite valores nulos (NULL)."}, "auto_increment": {"label": "Auto Incremento", "description": "Numera automaticamente cada nova linha."}}, "actions": {"create_fallback": "<PERSON><PERSON><PERSON>"}, "errors": {"update_failed": "Falha ao editar coluna", "create_failed": "<PERSON>alha ao criar coluna"}}, "METADATA": {"attr_types": {"type": "<PERSON><PERSON><PERSON> da coluna", "Date": "Data", "SingleLineText": "Texto", "Number": "Numero", "SingleChoice": "Seletor unico", "MultiChoice": "<PERSON><PERSON><PERSON> multiplo", "Checkbox": "CheckBox", "LinkDimension": "Link para outra dimensão", "SecuritySingleLink": "Segurança corporativa", "SecurityMultiLink": "Segurança corporativa", "MultiLinkToUser": "Link para usuário", "SingleLinkToUser": "Link para usuário", "Dropdown": "Dropdown", "Colorpicker": "Colorpicker", "Toggle": "Toggle"}, "creator": "<PERSON><PERSON><PERSON><PERSON>"}, "ONBOARDING": {"learn_by_doing": "Aprenda fazendo", "learn_by_pratice": "Aprenda na prática como utilizar o Mitra para criar seus Apps", "create_database": "Crie e gerencie bancos de dados com facilidade", "create_interface": "Projete e construa interfaces com seus dados", "create_functions": "Construa funcionalidades para seus componentes", "integrate_with": "Integre o Mitra com qualquer coisa", "launch": "Lance e escale seu software sem complicações", "open_our_playlist": "Acesse nossa playlist do Youtube", "create_tables": "<PERSON><PERSON> tabelas", "import_data": "Importe dados", "jdb_and_files": "JDBC e arquivos", "security_and_profiles": "Perfis e Segurança", "version_and_publish": "Versões e Publicação", "data": "<PERSON><PERSON>", "youtube_playlist": "Playlists do Youtube", "know_templates": "Conheça seu template", "community": "Comunidade", "talk_to_support": "Fale com o suporte", "customize": "Personalização", "interaction": "Interações", "create_database_actions": "Crie ações de database", "create_mitra_actions": "Crie ações do Mitra", "create_screens": "Crie telas", "config_components": "Configure componentes", "create_forms": "<PERSON><PERSON> formulá<PERSON>", "create_detail_modals": "Crie modais de de<PERSON>hes", "docs": "Documentação", "integrate": "Integração", "logic": "Lógica", "lauching": "Lançamento", "courses": "Cursos"}, "DIMENSIONS": {"create_error": "Falha ao cria<PERSON>", "delete_error": "Falha ao deletar Di<PERSON>ão", "update_error": "Falha ao atualizar <PERSON>", "data_entry_error": "Falha ao adicionar valor", "attributes": {"dynamic_toggle": "Permite inserir uma query com uma condição dinâmica, exibindo os resultados como se fosse um atributo.", "duplicate_pk": "Atributos únicos não permitem valores duplicados", "add_attribute_error": "Falha ao criar atributo", "change_type_attribute_error": "Falha ao trocar tipo do atributo", "precision": "Precisão", "add_name": "Adicionar nome", "add_precision": "<PERSON><PERSON><PERSON><PERSON>", "add_option": "Adicionar <PERSON>", "add_dimension": "Adicionar cadastro", "select_dimension": "Selecionar cadastro", "multiple_selection": "Seleção multipla", "delete_attribute": "Deletar atributo", "delete_attribute_error": "Falha ao deletar atributo", "delete_dimension": "Deletar cadastro"}, "attribute_with_this_name_already_exists": "Já existe um atributo com este nome, favor escolher outro.", "your_cube_cannot_have_2_connected_dimensions": "Seu cubo não pode ter 2 dimensões conectadas."}, "COLLABORATORS": {"invite_send_error": "Falha ao enviar convite", "invite_update_error": "Falha ao atualizar convite", "invite_validate_error": "Falha ao validar convite", "delete_error": "Falha ao deletar colaborator", "update_error": "Falha ao atualizar colaborator", "already_contributor": "Essa usuário já é colaborador nesse Workspace", "already_invited": "Essa usuário já possui um convite pendente", "already_creator": "Essa usuário j<PERSON> criador no Workspace desse projeto", "invitation_user_different": "O link de convite não corresponde ao usuário logado"}, "PLAN": {"free_credits_limit": "<PERSON><PERSON><PERSON> atingiu o limite de créditos de cortesia.", "complimentary_credits": "Os 5 créditos de cortesia já foram utilizados.", "limit_alert_1": "Você atingiu o limite diário do plano gratuito.", "limit_alert_2": "Configure sua chave do Gemini para continuar desenvolvendo.", "contract_plan": "Contratar plano", "project_frozen": "PROJETO CONGELADO", "payment_failed": "Falha no pagamento", "payment_error": "Houve um erro ao processar seu pagamento. Por favor, tente novamente ou utilize um método de pagamento diferente.", "until_day": "até o dia", "observation": "Observação: Você continuará utilizando todos os recursos do plano", "cancel_subscription_warning": "Você está prestes a cancelar sua assinatura. Esta ação resultará na perda de alguns benefícios do seu plano atual. Tem certeza de que deseja prosseguir?", "try_again_later": "Por favor, tente novamente mais tarde ou entre em contato com o suporte para mais informações.", "unavailable_content": "Ops! Este conteúdo está temporariamente indisponível", "size_descending": "<PERSON><PERSON><PERSON> (Descrescente)", "size_ascending": "<PERSON><PERSON><PERSON> (Crescente)", "used": "utilizados", "increase_storage": "Aumentar armazenamento", "storage_summary": "Resumo de Armazenamento do Banco de dados", "storage_limit_reached_message_v2": "Seu limite de armazenamento foi atingido, restringindo temporariamente algumas funcionalidades. Para restaurar o acesso completo, revise seu uso atual ou considere uma atualização de plano.", "storage_limit": "Limite de armazenamento atingido.", "storage_limit_reached_message": "Seu limite de armazenamento foi atingido, e algumas funções estão temporariamente bloqueadas. Revise o uso atual para liberar espaço ou considere atualizar seu plano para restaurar o acesso completo às funcionalidades", "storage_limit_reached": "Limite de armazenamento atingido. Revise seu uso ou atualize seu plano.", "edit_payment_method": "Editar forma de pagamento", "payment_info_correction_needed": "é necessário corrigir as informações de pagamento", "payment_not_processed": "Sua forma de pagamento não foi processada. Para continuar tendo acesso ao plano", "until_this_date": "até essa data", "plan_change_warning": "Você poderá continuar utilizando todos os recursos do plano", "plan_change_notice": "seu plano será alterado para o", "from_day": "A partir do dia", "attention": "Atenção", "plan_downgrade_warning": "Você está optando pelo downgrade do seu plano para o Starter. O novo valor será cobrado a partir da próxima fatura, em 16 de Novembro. Até 16 de Novembro, você continuará utilizando todos os recursos do plano Scale.", "current": "Atual", "cancel_subscription": "<PERSON><PERSON><PERSON>", "change_plan": "Alterar Plano", "manage_plan": "Gerenciar assinatura", "payment_failure_update_info": "Sua forma de pagamento falhou. Atualize suas informações de pagamento.", "payment_failure": "Falha de pagamento", "paid": "Pago", "show_invoice": "<PERSON><PERSON>", "status": "Status", "value": "Valor", "billing_date": "<PERSON> da Cobrança", "page": "<PERSON><PERSON><PERSON><PERSON>", "next_payment_date": "Próxima data de pagamento", "view_invoice": "Clique em “Ver Fatura” para fazer o download", "invoice_history": "Histórico de Faturas", "credit_card_ending_in": "Cartão de crédito terminado em", "method_payment": "Forma de pagamento", "card_helper": "Ao confirmar sua assinatura, você permite que Mitra cobre você em referência a pagamentos futuros em conformidade com os termos da empresa. Você pode cancelar a assinatura quando quiser.", "country_or_region": "<PERSON><PERSON> ou região", "due_date": "Vencimento", "card_number": "Número do cartão", "card_name": "Nome no Cartao", "payment_method": "Método de pagamento", "contact_email": "E-mail para contato", "add_promo_code": "Adicionar código promocional", "tax": "Imposto", "monthly_charge_quantity_1": "Qtde 1, Cobrado mensalmente", "per_application": "por aplicação", "plan": "Plano", "permonth": "por mês", "upgrade": "upgrade", "subscribe_plan": "<PERSON><PERSON><PERSON>", "payment_confirmation": "Confirmação de pagamento", "other_plans": "Outros planos", "database": "Banco de dados", "attachments": "Anexos", "free": "<PERSON><PERSON><PERSON><PERSON>", "actual_plan": "Plano atual", "be_an_early_adopter": "Se<PERSON> um early adopter e teste gratuitamente", "enjoy_beta": "Aproveite a oportunidade de explorar nossa versão beta em primeira mão e nos ajude a moldar o futuro do Mitra", "trial_remaining": "dias de teste", "daily_limit_reached": "Limite <PERSON><PERSON><PERSON>", "start_now": "<PERSON><PERSON><PERSON>ra", "developer": "<PERSON><PERSON><PERSON><PERSON>", "corporate": "Corporativo", "build_saas": "Construa e venda aplicações \n SaaS com a sua marca!", "build_corp": "Construa aplicações internas \n personalizadas para sua empresa!", "yearly": "<PERSON><PERSON>", "call_sales": "Falar com vendas", "recommended": "Recomendado", "monthly": "Mensal", "monthly_by": "por mês", "yearly_by": "por ano", "plans_and_prices": "Planos e preços", "yearly_bid": "Cobrado anualmente", "you_are_on": "Você está no", "free_trial": "Teste gratuito", "your_trial_period_ends_in_n_days": "Seu período de avaliação acaba em {days} dias.", "or_add_coupon": "adicione um cupom", "enjoy_all_benefits": "para aproveitar todos os benefícios do Mitra!", "by_application": "Por aplicação", "per_business_user": "por Usuário Business", "per_developer_user": "por <PERSON><PERSON><PERSON><PERSON>", "custom_features": "Podemos negociar para melhor atender os seus requisitos personalizados!", "free_forever": "<PERSON><PERSON><PERSON><PERSON> para sempre", "features": {"development_version": "Versão de desenvolvimento", "database_lines": "{count} linhas de banco de dados", "database_size": "{size} de banco de dados", "daily_loads": "{count} carregamentos/ dia", "email_send": "{count} disparos de e-mail/ dia", "all_features": "<PERSON>das as funcionalidades", "unlimited_developers": "Desenvolvedores ilimitados", "unlimited_database_lines": "Linhas de banco de dados ilimitadas", "use_your_brand": "Publicar aplicação com sua marca e domínio", "publish_on_marketplace": "Publicar no Marketplace", "attachments_size": "{size} de anexos", "backup_days": "Backup dos últimos {days} dias", "daily_guest_access": "{count} acessos sem login/ dia", "support_outside_community": "Suporte fora da comunidade", "free_templates": "Templates gratuit<PERSON>", "up_to_users": "Até {count} usu<PERSON><PERSON>s", "own_domain": "<PERSON><PERSON><PERSON>", "unlimited_ai": "IA sem limite de requisições", "sync_database": "Sincronize seu banco para utilizar sem limite de armazenamento"}, "plans": {"ENTERPRISE": "ENTERPRISE", "GROWTH": "SCALE", "SCALE": "SCALE", "FREE": "FREE", "STARTER": "STARTER", "TEAM": "TEAM", "enterprise_descr": "Ideal para máxima segurança e escalabilidade", "growth_descr": "Ideal para escalar sua aplicação", "free_descr": "Ideal para aprender e construir", "corp_free_descr": "Para indivíduos que querem conhecer e começar a usar o Mitra em suas empresas", "scale_descr": "Ideal para escalar sua aplicação", "starter_descr": "Ideal para lançar sua aplicação", "team_descr": "Ideal para escalar sua equipe e negócio", "pro_descr": "Para pequenas equipes que desejam centralizar e otimizar seus processos operacionais", "corp_enterprise_descr": "Ideal para equipes maiores, com máxima segurança e escalabilidade"}, "cpf_cnpj_mandatory": "CPF/CNPJ é obrigatório", "name_card_mandatory": "Nome no cartão é obrigatório", "card_number_required": "Número do cartão é obrigatório", "expiration_date_mandatory": "Data de expiração é obrigatória", "cvv_is_mandatory": "CVV é obrigatório"}, "WORKSPACES": {"ai_settings": "Configura<PERSON><PERSON><PERSON> da {name} (IA)", "view_more_courses": "<PERSON><PERSON> <PERSON>", "view_all_documentation": "Ver Toda a Documentação", "new_tag": "Novidade!", "email_verification": "Após cadastrar um e-mail, a ", "email_verification_aws": "Amazon Web Services (", "email_verification_2": " enviará uma confirmação para ele. Basta abrir o e-mail, ", "email_verification_link": "clicar no link de confirmação", "email_verification_3": " e pronto! Depois, volte para o Mitra e o seu e-mail estará verificado.", "verify_email": "Verifique seu e-mail", "enter_mail_name": "Informe o nome do e-mail", "enter_mail": "Informe o e-mail", "no_environment_start": "Você ainda não possui nenhum Ambiente criado. Vamos começar?", "no_workspace_start": "Você ainda não possui nenhum Workspace do tipo {name} ", "no_workspace_start_2": "criad<PERSON>. <PERSON><PERSON>s começar?", "target_workspace": "Workspace destino", "validating_domain": "Validando domínio", "no_interface": "Nenhuma interface foi criada até o momento.", "no_action": "Nenhuma ação foi criada até o momento.", "remove_skill": "Remover skill", "settings_desc": "Gerencie os membros do seu projeto, as permissões das suas contas e a assinatura do seu aplicativo.", "user_limit_reached": "Limite de usuários atingido", "invalid_email": "E-mail inválido", "trial_info": "Tempo em que o seu usuário vai poder testar a ferramenta.", "trial_time": "Tempo de Trial", "monthly_info": "Os valores recebidos serão destinados 70% ao criador da solução e 30% à plataforma Mitra.", "monthly_payment": "Mensalidade", "banner_title": "Banner de apresentação", "logo_info": "Atualize o logo da sua solução.", "header_info": "Atualize os detalhes da sua solução aqui. Essas informações vão aparecer no Marketplace.", "desc_info": "Descreva sua solução em até 200 caracteres.", "tags_info": "Selecione até 8 tags.", "name_info": "Esse nome será exibido tanto para os usuários como no marketplace.", "company_info": "Você precisa estar cadastrado como uma empresa parceira para publicar seu aplicativo.", "published_solution": "Solução Publicada", "input_create_workspace": "Qual será o nome do seu Workspace?", "configure_workspace": "Configurar Workspace", "confirm_delete_project": "Tem certeza que quer deletar o Projeto? Essa ação não pode ser desfeita!", "created": "Workspace criado com sucesso", "create_error": "Falha ao criar Workspace", "removed": "Workspace removido com sucesso", "remove_error": "Falha ao remover Workspace", "no_database": "Você ainda não possui nenhum projeto", "only_creator_manage": "Ação apenas para criadores", "manage_members": "Gerenciar Membros", "manage_workspace": "Gerenciar workspace", "delete_workspace_title": "Tem certeza que deseja excluir esse workspace?", "delete_workspace_body": "Essa ação não podera ser desfeita.", "invite_members_create_projects": "Convide membros para que eles possam criar projetos", "invite_users": "<PERSON><PERSON><PERSON>", "users_who_create_projects": "Usuários que criam projetos", "create_new": "Criar workspace", "return_home": "Voltar para Home", "settings": "Configurações", "settings_off": "Configurações de ", "account_type": "Tipo de Conta", "project_limit": "Limite de Projetos", "purchased_users": "Usuários Comprados", "solution_site": "Site da Solução", "enter_solution_site": "Insira o site da sua solução", "unique_users": "<PERSON><PERSON><PERSON><PERSON>", "storage_files": "Armazenamento | Arquivos", "storage_bank": "Armazenamento | Banco", "of": "de", "users": "usuários", "projects": "Projetos | Projeto", "registration_date": "Data de Cadastro", "bank": "Banco", "files": "<PERSON>r<PERSON><PERSON>", "file": "Arquivo", "developer_user": "<PERSON><PERSON><PERSON><PERSON>", "developers": "<PERSON><PERSON><PERSON><PERSON>", "developer": "<PERSON><PERSON><PERSON><PERSON>", "business_user": "Usuário Business", "uploads": "Carregamentos (Por dia)", "uploads_day": "Carregamentos hoje", "accesses": "<PERSON><PERSON><PERSON> (Por dia)", "boradcasting": "Boradcasting (Por dia)", "summary": "Resumo do Workspace", "name": "Nome", "workspace_information_general": "Informações Gerais", "information": "Informações", "user": "Usuários", "admins": "Administradores", "admin": "Administrador", "creators": "<PERSON><PERSON><PERSON>", "subscriptions": "Assinaturas", "workspace_information": "Informações do Workspace", "add_image": "Adicionar imagem", "my_team": "<PERSON><PERSON>", "registered_users": "Usuários Cadastrados", "loads_today": "Carregamentos (Hoje)", "loads": "Carregamentos", "email": "Disparos de E-mail (Hoje)", "entry": "Acessos", "guest_entry": "Acesso de convidados hoje", "broadcastings": "disparos", "create_projects": "<PERSON><PERSON><PERSON> projetos", "edit_projects": "<PERSON><PERSON>", "my_first_workspace": "Meu Primeiro Workspace", "edit_projects_single": "<PERSON><PERSON>", "role": "Função", "access_without_login": "<PERSON><PERSON> sem login (Hoje)", "deleting_your_workspace": "Apagar seu Workspace é uma ação irreversível que resultará na exclusão completa de seus projetos e dados, sem possibilidade de recuperação.", "new_user": "<PERSON><PERSON><PERSON>", "delete_project": "Apagar Projeto", "sure_to_delete_the": "Tem certeza que deseja apagar o ", "sure_to_remove_the": "Tem certeza que deseja remover a skill", "project": "Projeto | projeto", "delete_workspace_warning": "Esta ação não pode ser desfeita e todos os dados serão perdidos permanentemente. A assinatura associada continuará ativa até o final do ciclo de faturamento, sem reembolso pelo período não utilizado.", "this_action_cannot_be_undone": "Essa ação não pode ser desfeita e os dados serão perdidos permanentemente", "have_reached_user_limit": "Você atingiu o limite de membros permitidos no workspace.", "consider_a": "Considere um", "plan_upgrade": "Upgrade do Plano", "delete_workspace": "Apagar Workspace", "or_manage_users_invite": "ou gerencie os usuários da sua equipe para liberar mais convites.", "this_action_is": "Essa ação é", "irreversible": "irreversível", "and_will_result_in": "e resultará na", "complete_deletion": "exclusão completa", "your_projects_and_data": "de seus projetos e dados,", "with_possibility_recovery": "sem possibilidade de recuperação.", "publish_solution": "Publicar solução", "publish_with_domain": "Publicar com meu domínio", "publish_app": "Publicar app", "publish": "Publicar", "no_projects_published": "Nenhum projeto publicado", "my_projects": "Meus <PERSON>", "my_workspaces": "Meus Workspaces", "project_downloaded_customers": "Projetos Baixados Por Clientes", "published_in": "Publicado em", "in_production": "Em produção", "pending_approval": "Pendente aprovação", "pending_company_approval": "Pendente aprovação empresa", "new_version": "Nova versão", "publish_your_domain_information": "Para publicar com seu domínio, precisamos de algumas informações:", "per": "por", "subscribe_free": "<PERSON><PERSON><PERSON> (Gratuito)", "subscribe": "<PERSON><PERSON><PERSON>", "monthly_per_user": "Mensal / por Us<PERSON>á<PERSON>", "about": "Sobre", "developed_by": "Desenvolvido por", "resources": "Recursos:", "tutorial": "Tutorial", "developer_website": "Site do desenvolvedor", "terms_of_use": "Termos de uso", "categories": "Categorias: | Categoria", "free": "Gratuito | Gratuitos", "user_month": "/usuário/mês", "per_month": "/mês", "you_dont_have_any_projects_created_yet": "Você ainda não possui nenhum projeto criado.", "you_are_just_guest": "Você é apenas um desenvolvedor convidado nesse workspace.", "create_workspace": "Criar um workspace", "free_plan": "Plano gratuito", "member": "membro", "upgrade_your_plan": "Fazer upgrade de plano", "toggle_workspace": "ALTERNAR WORKSPACE:", "paid": "Pagos", "for_what_purpose_you_intend_to_use": "Para qual finalidade você pretende usar?", "want_to_create_customized_solutions": "Quero criar soluções personalizadas para as minhas prórias operações.", "want_to_create_solutions_community": "Quero criar soluções e vender para a comunidade.", "enter_project_name": "Insira o nome do projeto", "select_the_category": "Selecione a categoria", "company": "Empresa", "enter_company_name": "Insira o nome da empresa", "company_logo": "Logo empresa", "insert_the_company_logo_link": "Insira o link da logo da empresa", "description": "Descrição", "enter_description": "Insira a descrição", "solution_home_screen_id": "ID da Tela Inicial da solução", "enter_home_screen_id": "Insira o ID da tela inicial", "lock_cube_id": "Id do Cubo de bloqueio", "enter_lock_cube_id": "Escolha o cubo de bloqueio", "lock_screen_id": "Escolha a tela de bloqueio", "enter_lock_screen_id": "Insira o Insira o ID da tela de bloqueio", "tell_about_project": "Fale um pouco sobre o projeto", "solution_logo": "Logo da solução", "insert_solution_logo": "Insira a logo da solução", "onboarding_video": "<PERSON><PERSON><PERSON><PERSON> onboarding", "insert_onboarding_video_link": "Insira o link do vídeo onboarding", "promotion_video": "Vídeo divulgação", "insert_promotional_video_link": "Insira o link do vídeo de divulgação", "media_disclosure": "Imagem de divulgação 1", "insert_promotional_media_link": "Insira a imagem de divulgação 1", "media_disclosure_2": "Imagem de divulgação 2", "insert_promotional_media_link_2": "Insira a imagem de divulgação 2", "media_disclosure_3": "Imagem de divulgação 3", "insert_promotional_media_link_3": "Insira a imagem de divulgação 3", "laod_data": "Carregar dados", "chat_with_your_data": "Converse com seus dados", "hello": "O<PERSON><PERSON>", "welcome_to_mitra": "bem vindo ao <PERSON>!", "dont_have_projects_created_yet_title": "Ainda não existem projetos nesse Workspace.", "dont_have_projects_created_yet_subtitle": "Clique em uma das opções abaixo para começar.", "start_from_scratch": "Iniciar do <PERSON>", "start_with_templates": "Iniciar com Templates", "search_screens": "Buscar telas", "search_modules": "Buscar módulos", "artificial_intelligence": "Inteligência Artificial", "please_enter_project_name": "Por favor, insira o nome do projeto", "please_select_category": "Por favor, selecione uma categoria", "field_required": "Esse campo é obrigatório", "please_enter_company_name": "Por favor, insira o nome da empresa", "please_insert_company_logo": "Por favor, insira o logo da empresa", "please_enter_description": "Por favor, insira uma descrição", "please_enter_home_screen": "Por favor, insira a tela inicial", "please_enter_information_about_project": "Por favor, insira informações sobre o projeto", "please_insert_logo_image": "Por favor, insira a imagem do logo", "please_insert_onboarding_video": "Por favor, insira o vídeo de onboarding", "please_insert_marketing_video": "Por favor, insira o vídeo de marketing", "please_insert_promotional_media_link_1": "Por favor, insira o link da mídia de divulgação 1", "please_insert_promotional_media_link_2": "Por favor, insira o link da mídia de divulgação 2", "please_insert_promotional_media_link_3": "Por favor, insira o link da mídia de divulgação 3", "main_subdomain": "Subdomínio", "enter_main_subdomain": "Insira o Subdomínio principal", "secondary_subdomain": "Subdomínio secundário", "enter_secondary_subdomain": "Insira o Subdomínio secundário", "email_sending_service": "E-mail para o serviço de envio de e-mails", "enter_email": "Insira e-mail", "enter_image_url": "Insira a url da imagem", "enter_image_url_or_upload": "Insira a url ou faça upload do Arquivo", "solution_icon": "Icone da solução", "uploader_type": " .jpg e .png aqui", "support_email": "E-mail de Suporte", "enter_support_email": "Insira o e-mail de suporte", "support_name": "Nome de Suporte", "enter_support_name": "Insira o nome de suporte", "number_trial_days": "Quantidade de dias Trial", "enter_number_trial_days": "Insira a quantidade de dias Trial", "monthly_value": "<PERSON>or mensal", "enter_monthly_value": "Insira o valor mensal", "remove_user": "Remover usuário", "are_sure_to_remove_user": "Tem certeza que deseja remover o usuário", "specialties": "Especialidades", "see_details": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "add_skill": "<PERSON><PERSON><PERSON><PERSON>", "build_personalized_bia_profiles": "Construa perfis personalizados da AI Copilot", "new_skill": "Nova Skill", "community": "Comunidade", "one_project_per_base": "Meu projeto terá uma base por cliente", "each_client_have_different_database": "Cada cliente vai ter uma base de dados diferente.", "you_have": "Você tem", "workspaces_developer_mode": "Workspaces no Modo Desenvolvedor", "workspaces_enterprise_mode": "Workspaces no Modo Enterprise"}, "WHITE_LABEL": {"save_version": "<PERSON><PERSON>", "save_and_update": "Salvar e Atualizar", "publishing_tip_01": "Seu projeto está sendo publicado.", "publishing_tip_02": "Por favor, aguarde enquanto finalizamos o processo. Ele estará pronto para uso em breve!", "to_add_custom_domain": " e adicionar um domínio personalizado.", "publish_your_project": " publicar seu projeto ", "click_to_publish": "Pressione Publicar para ", "in_domain": " no domínio", "not_published": "Não publicado", "published_and_optmized": "Projeto publicado com sucesso!  🎉", "unpublish": "Despublicar", "validating_domain": "Validando domínio", "unpublish_all": "Despublicar tudo", "publishing": "Publicando", "web_solution": "Solução Web", "mobile_solution": "Solução Mobile", "mitra_apps_terms": "Ao publicar no Mitra Apps, as pessoas poderão ver e utilizar sua solução através do aplicativo “Mitra Apps”. Você poderá lançar seu aplicativo whitelabel no futuro, mas essa é uma opção mais rápida para disponibilizar sua solução para os usuários finais. As submissões deverão ser aprovadas pelo nosso time.", "images": "Imagens", "mitra_apps_publish": "Publicar projeto no Mitra Apps", "images_tip": "No máximo 6 - <PERSON><PERSON><PERSON>", "insert_video_link": "Insira o link do vídeo", "short_description": "Descrição curta", "long_description": "Descrição longa", "type_description": "Escreva sua descrição aqui", "mitra_apps_hint": "Seu projeto será publicado em nossa Loja de Aplicativos Mitra e poderá ser utilizado pelos usuários.", "app_name": "Nome do app", "project_publish_hint": "Projeto que será publicado.", "domain": "<PERSON><PERSON><PERSON>", "base_domain": "Domínio base", "custom_domain": "<PERSON><PERSON><PERSON>", "base_domain_info": "Domínio gerado automaticamente pelo sistema para acesso principal à sua aplicação.", "custom_domain_info": "Crie um domínio personalizado utilizando o subdomínio do Mitra ou conecte a um domínio que você já possui.", "get_mitra_subdomain": "Obtenha um subdomínio Mitra", "get_mitra_subdomain_hint": "Conecte instantaneamente um domínio personalizado do Mitra.", "connect_custom_domain": "Conectar um domínio que você possui", "connect_custom_domain_hint": "Conectar um domínio adquirido através de um serviço de hospedagem.", "logo_hint": "A logo será utilizada na tela de login e na barra de navegação.", "primary_color": "Cor principal", "color_hint": "A cor principal será utilizada na tela de login e demais telas nativas.", "user_permission": "Liberações para o usuário final", "home_screen": "Tela inicial da solução*", "home_screen_hint": "Esta é a tela que abre por padrão quando o usuário entra na aplicação.", "default_profile_hint": "O perfil padrão vai ser associado a todos os usuários quando cadastrarem", "enable_mobile": "Ativar versão mobile para este projeto", "display_project_on_mobile": "O projeto aparece na lista de projetos dentro do mobile", "project_settings": "Configurações do projeto", "published": "Publicado", "edit_published_app": "Editar publicação", "default_profile": "<PERSON><PERSON><PERSON>", "publish_project": "Publicar projeto", "unpublish_project": "Despublicar Projeto", "already_in_use": "Esse domínio já está sendo utilizado por outro projeto.", "invalid_domain": "<PERSON><PERSON><PERSON>"}, "FOREIGN_KEY_MENU": {"add_connection": "Adicionar <PERSON> com outra tabela", "labels": {"select_schema": "Selecione um Esquema", "select_reference_table": "Selecione a tabela de referência", "select_columns_for_reference": "Selecione colunas de {tableName} para referenciar"}, "placeholders": {"select_schema": "Selecione um Esquema", "select_table": "Selecione a tabela", "select": "Selecionar"}, "headers": {"reference_table_fallback": "Tabela de Referência"}, "buttons": {"add_column": "<PERSON><PERSON><PERSON><PERSON>"}}, "DRIVE": {"storage": "Armazenamento", "add_file_button": "Use o botão '+ Adicionar Arquivo' para começar a preencher esta pasta com seus documentos.", "add_folder_button": "Use o botão '+ Nova Pasta' para criar sua primeira pasta e começar a organizar seus arquivos.", "empty_folder": "Esta pasta está vazia", "size": "<PERSON><PERSON><PERSON>", "add_file": "Adicionar arquivo", "new_folder": "Nova pasta", "my_folders": "Minhas pastas", "delete_folder": "Deletar Pasta", "delete_file": "Deletar Arquivo", "delete_folder_warning_1": "Você está prestes a deletar a pasta", "delete_folder_warning_2": "Todo conteúdo será perdido e essa ação não poderá ser desfeita.", "delete_file_warning_1": "Você está prestes a deletar o arquivo", "delete_file_warning_2": "Esta ação é permanente e não poderá ser desfeita.", "confirm_continue": "Tem certeza de que deseja continuar?"}, "DATABASE": {"allows_public_screen": "Permite ser executado a partir de uma tela pública", "public": "Público", "add_table_placeholder": "Adicionar_tabela", "date_format": "Formato de Data", "not_configured": "<PERSON>ssa tabela ainda não foi configurada, favor alterar a consulta.", "not_column": "Essa não é uma coluna da tabela atual. Os dados exibidos aqui fazem parte de outra tabela que está conectada à esta.", "input_variable": "Variáveis de Inputs", "global_variables": "Variáveis <PERSON>", "global_variable": "Variável Global", "this_screen": "(Nesta tela)", "temporary_value_description": "Preencha um valor temporário para testar diferentes cenários. Ele não será armazenado.", "native_variables": "Variáveis Nativas", "no_variables_found": "<PERSON><PERSON><PERSON><PERSON> vari<PERSON>vel encontada.", "variable_already_exists": "<PERSON><PERSON> <PERSON>e variável", "temporary_value": "Valor temporário", "native_table": "Tabelas Nativas", "only_fist_value": "Para este input, apenas o valor da primeira linha e da primeira coluna será considerado.", "sum": "<PERSON><PERSON>", "selector": "<PERSON><PERSON><PERSON>", "average": "Média", "count": "Contagem", "default_aggregation_function": "Função de agregação padrão", "registrations": "Cadastros", "script_not_execulted": "O script não será realmente executado, apenas testado!", "not_allowed_script": "Não é permitido rodar Script em uma Query. Para isso, crie um Script novo.", "not_allowed_query": "Não é permitido rodar <PERSON> (Consulta) em um Script. Para isso, crie uma Query nova.", "entity": "Entidade", "view_table": "Tabela de Visualização", "edit_table": "<PERSON><PERSON><PERSON>", "may_take_while": "Essa ação pode demorar um pouco e gerar necessidade de ajuste em algumas de suas queries.", "wish_continue": "Deseja continuar?", "recreate_database": "Recria o metadado do seu banco de dados, trazendo atualizações de versão e ajustando suas VIEWs.", "test": "<PERSON>ar", "run": "Executar", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "invalid_script": "Script inválido", "validated_script": "Script validado", "alowed_scrip": "Nesta lista, aparecem somente as tabelas em que é permitido fazer script.", "script_context": "Contexto do Script", "import_csv": "Importar CSV", "version_warning": "Ao personalizar o nome do atributo, será criada uma nova versão para o Cadastro.", "customize_attribute_name": "Personalizar nome do atributo", "path": "<PERSON><PERSON><PERSON>", "lots_of_content": "Mais de 10 colunas", "data": "<PERSON><PERSON>", "sure_unlink_dimension": "Tem certeza que deseja desvincular o cadastro", "in_cube": "no cubo", "change_query": "Alterar consulta", "clear_cube": "Limpar cubo", "clear_dimension": "Limpar cadastro", "type_search": "Digite para buscar", "clean_cube": "Limpar esse cubo", "clean_dimension": "Limpar esse cadastro", "sure_clean_cube": "Tem certeza que deseja limpar o cubo", "sure_clean_dimension": "Tem certeza que deseja limpar o cadastro", "all_data_cube_deleted": "Todos os dados desse cubo serão apagados", "all_data_dimension_deleted": "Todos os dados desse cadastro serão apagados", "dynamic_attribute": "Atributo Dinâ<PERSON>", "add_dimension": "Adicionar cadastro", "enter_name": "Insira um nome", "select_dimension": "Selecione um cadastro", "add_dimension_to_cube": "Adicione cadastros ao cubo", "data_dictionary": "Dicionário de dados", "escape_all": "Escapar ALL", "escaped_dimension": "Cadastro escapado", "used_cube": "O cubo está sendo utilizado em componentes de tela. Favor remover antes de deletar!", "used_dim": "A dimensão está sendo utilizada em componentes de tela. Favor remover antes de deletar!", "no_description": "Sem descrição", "no_registration": "Nenhum cadastro disponível.", "start_connect_record": "Comece criando um cadastro para conectar a outro registro.", "duplicate_id": "ID duplicado", "no_attribute": "Se<PERSON> ", "version": "Vers<PERSON>", "new_version": "Nova versão", "main_version": "Versão principal", "main_db": "Database principal", "cube_versioning": "Versionamento de cubo", "auto_incremental": "Auto incremental", "date": "Data", "numeric": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Inteiro)", "text": "Texto", "group": "Grupo", "exhibition": "Exibição", "code": "Código", "both": "Ambos", "result": "<PERSON><PERSON><PERSON><PERSON>", "invalid": "<PERSON>v<PERSON><PERSON><PERSON>", "ordination": "Ordenação", "column_value_type": "<PERSON><PERSON><PERSON> de coluna valor", "dynamic_cube": "<PERSON><PERSON><PERSON>", "configure_query": "Configurar consulta", "run_query": "Executar Query", "close_all_tabs": "<PERSON><PERSON><PERSON> as abas", "auxiliary_table": "<PERSON>bela auxiliar", "query_message": "Configure e insira uma consulta para visualizar os dados.", "last_execution": "<PERSON><PERSON><PERSON>", "last_execution_rows": "linha em | linhas em", "data_loader_log": "Log da importação", "data_loader_not_have_logs": "Esta importação não possui nenhum registro de log no momento.", "sync_in_progress": "Sincronização em andamento", "data_list": "Lista de dados", "welcome_database": "Bem vindo ao Database", "select_data": "Selecionar ou criar objetos de banco de dados", "existing_data": "Selecionar dado existente", "enlarged_view": "Visualização ampliada", "encrypt": "Encrypt", "log": "Log", "new_group": "Novo Grupo", "hide_in_drill": "Esconder no drill", "alert_lose_data": "Todo o conteúdo da dimensão e dos cubos vinculados a ela serão perdidos. Deseja continuar mesmo assim?", "only_colarborators": "Voce nao possui acesso a esse projeto!", "create_error": "Falha ao criar projeto", "remove_error": "Falha ao remover projeto", "open_error": "Falha ao acessar projeto", "new_table": "Nova tabela", "table": "Tabela | Tabelas", "new_query": "Nova query", "query": "Query", "foreign_key": "Chave composta", "simple_key": "Chave simples", "analysis_dataset": "Dataset de análise", "create_dynamic_cube": "<PERSON><PERSON><PERSON>", "type": "Tipo", "select": "Selecionar", "register": "Cadastro", "cube": "<PERSON><PERSON><PERSON>", "dimension": "Cadastro", "add_entries_to_cube": "Adicione cadastros ao cubo", "insert_nickname": "Inserir apelido", "find_record": "Encontre um cadastro", "edit_attribute": "<PERSON><PERSON>", "new_attribute": "Novo Atributo", "unique_key": "Chave <PERSON>", "color": "Cor", "choose_option_color": "Escolha a cor da opção", "more_colors": "Mais cores", "option": "Opção", "new_option": "Nova opção", "select_the_registration": "Selecione o cadastro", "add_new_dimension": "Adicionar novo cadastro", "add_new_jdbc": "Adicionar novo JDBC", "new_dimension": "Novo cadastro", "allow_linking_multiple_records": "Permitir ligar a multiplos registros", "dimension_type": {"numeric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "Texto", "date": "Data", "single_selector": "<PERSON><PERSON><PERSON>", "multi_selector": "<PERSON><PERSON><PERSON>", "connect_another_record": "FK", "corporative_security": "Segurança corporativa", "automation": "Automação"}, "automation": {"automation_bg": "<PERSON><PERSON> <PERSON>", "automation_font": "<PERSON><PERSON>", "automation_bold": "Negrito", "automation_prefix": "Prefixo", "automation_sufix": "Sufixo"}, "add_row": "<PERSON><PERSON><PERSON><PERSON>", "add_column": "<PERSON><PERSON><PERSON><PERSON> coluna", "sure_to_delete_script": "Tem certeza que deseja excluir o script", "sure_to_delete_query": "Tem certeza que deseja excluir a query", "sure_to_delete_action": "Tem certeza que deseja excluir a ação", "sure_to_delete_free_db_online_table": "Tem certeza que deseja excluir a tabela online", "sure_to_delete_data_loader": "Tem certeza que deseja excluir a importação", "sure_to_delete_dimension": "Tem certeza que deseja excluir o cadastro", "sure_to_delete_attribute": "Tem certeza que deseja excluir o atributo", "table_sync_data": "<PERSON>a tabela \"{name}\" já sincronizada terá seus dados resetados.", "sure_to_delete_cube": "Tem certeza que deseja excluir o cubo", "action_cannot_undone": "Essa ação não poderá ser desfeita.", "records": "Registros", "record": "Registro", "description": "Descrição", "sure_delete": "Tem certeza que deseja excluir a ", "destination_cube": "<PERSON><PERSON><PERSON>", "cube_bound": "Cubo que limita interseções dos cálculos", "visual_customization": "Personalização Visual", "select_or_create": "Selecione ou crie", "general": "G<PERSON>", "first_option_in_the_list_is_default": "A primeira opção da lista é a padrão", "automatically_created_item": "Este item é criado automaticamente para ser a opção padrão quando esta tabela for referenciada em um atributo “Conectar a outro registro”", "hint_text_character": "Você pode inserir até {max} caracteres.", "characters_remaining": "{remaining} caracteres restantes", "you_text_hit_max_characters": "Limite de 58 caracteres atingido.", "table_name": "Nome", "cube_name": "Nome do cubo", "we_are_loading_your_data": "Estamos carregando seus dados", "meantime_your_can_browse_other_tables": "<PERSON>quanto isso você pode navegar por outras tabelas.", "preview_not_available": "O preview não está disponível!", "data_too_large_to_display": "Os dados desta tabela são muito grandes para serem exibidos aqui. Você ainda pode utilizá-los normalmente em seus Dashboards.", "filter_variables": "Variáveis de Filtro", "query_insert_table": "<PERSON><PERSON><PERSON> tabela", "query_insert_here": "Inserir aqui", "query_insert_variable": "<PERSON><PERSON><PERSON> var<PERSON>", "query_error_message": "Só são permitidas operações de INSERT, UPDATE ou DELETE", "variable": "Variável", "content": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "on_this_screen": "Está nessa tela?", "view_table_msg": "Tabelas de visualização incluem descrições das FKs, mas podem reduzir a performance. Use-as apenas quando necessário.", "add_table": "<PERSON><PERSON><PERSON><PERSON> tabela", "column_are_metrics": "Colunas que são métricas", "online_table": "Tabela Online", "attribute": "Atributo", "metric": "M<PERSON><PERSON><PERSON>", "new_metric": "Nova Métrica", "this_table_has_not_configured": "<PERSON>ssa tabela ainda não foi configurada", "no_results": "Sem resultados", "free_db": "Banco Analytics", "want_to_use_online_table": "Quero usar minhas tabelas online", "jdbc_error": "Ops, algo deu errado!", "jdbc_error_message": "Não conseguimos conectar ao banco selecionado no momento. Ele pode estar offline ou enfrentando instabilidade. Por favor, tente novamente mais tarde ou selecione outro banco para continuar.", "online_table_error": "Por favor verifique a query e o status do banco para continuar.", "synchronize_project_db": "Sincronize o projeto para utilizar um banco de dados externo com a nova base principal de desenvolvimento do seu sistema.", "soon_allow_external_db": "Em breve, permitiremos que você utilize bancos externos como seu banco principal no Mi<PERSON>, por enquanto, você pode conectar a bancos externos utilizando \"Tabela Online\" ou \"Importar\"", "connect_external_db": "Conecte-se a um banco externo para realizar Carregamentos de Dados ou criar uma Tabela Online, sem alterar o banco principal do projeto.", "configure_table_policy": "Configurar Policy da Tabela", "online_tables": "Tabelas Online", "url_file": "URL do Arquivo", "imports": "Importações", "synchronize": "Sincronizar", "query_preview": "Preview da Query", "imported_table_preview": "Preview da Tabela Importada", "successfully_synchronized_table": "<PERSON><PERSON><PERSON> `{tableName}` sincronizada com sucesso.", "no_spaces_allowed": "Não é permitido o uso de espaços.", "edit_data_loader": "Editar <PERSON>", "create_database_action": "Criar Ação de Database"}, "AI": {"ai_title": "IA", "show_old_posts": "Ver mais mensagens antigas", "executing_plan": "Executando Plano", "planning": "Planejando", "few_minutes": "<PERSON><PERSON> pode levar alguns minutos...", "creating_project": "<PERSON><PERSON><PERSON> seu projeto", "try_to_fix": "clique para arrumar", "failed": "<PERSON><PERSON><PERSON>", "watch_and_learn": "Assista e aprenda", "analyze_all_components": "<PERSON>lisar os componentes de todas as telas do sistema, não só da atual.", "fail_to_send": "Falha ao enviar prompt, tente novamente.", "key_connected": "Chave conectada com sucesso", "key_removed": "Chave removida com sucesso", "config_key_tip_1": "Utilizamos o modelo Gemini Pro 2.5, do Google.", "config_key_tip_2": "Cadastre uma chave de API para usar a {name} — ela será utilizada em todos os seus workspaces e projetos quando o uso da chave própria estiver ativado.", "config_key_tip_3": "Essa opção usa sua chave e evita consumir créditos do workspace, mas sua chave pessoal pode atingir o limite de uso.", "config_key_tip_4": "Cadastre uma chave de API para ativar a {name} neste workspace — ela será compartilhada entre todos os projetos e usuários.", "config_key_tip_5": "Para desativar esta opção, um administrador precisa configurar uma chave de API para o workspace.", "daily_credits": "Créditos <PERSON>", "use_free_plan": "Você está usando o plano gratuito", "do_upgrade": "Faça upgrade para créditos ilimitados", "no_chats": "Nenhum chat iniciado", "what_you_want_to_create": "O que você quer criar?", "prompt_placeholder": "Digite o que você quer criar", "simple_analysis": "<PERSON><PERSON><PERSON><PERSON>", "simple_analysis_description_mitra": "A {name} responde em cerca de 10 segundos, ideal para consultas objetivas que não exigem análises comparativas, geração de cálculos ou filtros avançados.", "simple_analysis_description_sk": "A AI Copilot responde em cerca de 10 segundos, ideal para consultas objetivas que não exigem análises comparativas, geração de cálculos ou filtros avançados.", "simple_analysis_examples": "Exemplos:\n 1. Quem me gerou mais margem neste ano?\n 2. Quanto eu tive de receita por mês?\n 3. Qual foi meu EBITDA por empresa no último mês?", "complex_analysis": "<PERSON><PERSON><PERSON><PERSON>", "complex_analysis_description_mitra": "No modo avançado, a {name} pode raciocinar por até 2 minutos, mas consegue responder com cálculos e filtros complexos, além de indicadores personalizados.", "complex_analysis_description_sk": "No modo avançado, a AI Copilot pode raciocinar por até 2 minutos, mas consegue responder com cálculos e filtros complexos, além de indicadores personalizados.", "complex_analysis_examples": "Exemplos:\n 1. Como está meu turnover?\n 2. Quais produtos tiveram redução de ticket médio em relação ao ano passado? E em % de margem?\n 3. Quantos clientes por vendedor estão a mais de 120 dias sem comprar?", "new_chat": "Novo Chat", "ai_chats": "Chats com IA", "ai_chat": "Chat com IA", "new": "Novo", "search": "Pesquisar...", "edit_key": "Editar chave", "send_new_message": "Envie uma nova mensagem...", "create_financial_management_project": "Faça um projeto de gestão financeira", "o1_mini_tooltip": "Ativar versão otimizada, 50% mais rápida e econômica", "to_start_configure_key": "Para comecar configure sua chave!", "configure_key": "Configurar chave", "do_not_know_where_start": "<PERSON>ão sabe por onde começar?", "know_me": "Me conheça", "how_did_arrive_this_result": "Como cheguei nesse resultado?", "create_dashboard_seconds": "Crie um Dashboard em segundos com a", "ai_know_me": "me conhe<PERSON>", "ai_dashboard_text_1": "Nossa Inteligência Artificial consegue transformar sua idéia em dashboard instantaneamente. Basta falar quais métricas e perspectivas você gostaria que estivessem no seu dashboard. Clique no botão", "ai_dashboard_text_2": "para ver quais dados esse skill consegue utilizar", "ai_dashboard_placeholder": "Quero um dashboard de vendas e margem de contribuição, incluindo uma visão por vendedor", "ai_dashboard_text_alternative": "Nossa Inteligência Artificial consegue transformar sua idéia em dashboard instantaneamente. Basta falar quais métricas e perspectivas você gostaria que estivessem no seu dashboard.", "chat_with_your_data": "Converse com os seus dados", "quick": "<PERSON><PERSON><PERSON><PERSON>", "advanced": "Avançada", "hi_i_am": "O<PERSON><PERSON>, eu sou a", "you_have_access_to_profiles": "Você possui acesso aos perfis:", "can_answer_you": "Consigo te responder", "this_makes_me_able_answer": "Isso me faz conseguir te responder", "questions_about_following_indicators": "perguntas sobre os seguintes indicadores:", "with_following_perspectives": "Com as seguintes perspectivas:", "if_missing_indicator_talk_administrator": "Se está faltando um indicador ou perspectiva importante pra você, por favor converse com o administrador da sua aplicação.", "choose_skill_start_chatting": "Escolha uma skill para começar o chat", "choose_project_start_chatting": "Escolha um projeto para começar o chat", "no_skills_found": "<PERSON><PERSON><PERSON><PERSON> skill encontrada", "no_projects_found": "Nenhum projeto encontrado", "do_not_have_profile_configured": "Você não possui um perfil configurado, contate um administrador para configurá-lo.", "do_not_have_key_configured": "Você não possui uma chave configurada, contate um administrador para configurá-la.", "to_start_create_profile_and_key": "<PERSON> começar, crie um ou mais perfis de acesso e configure sua chave!", "to_start_create_chat": "<PERSON> come<PERSON>r, crie um chat!", "configure_profiles": "Configurar perfis", "profile": "Perfil | Perfis", "configure": "configurado | configurados", "key_configured": "<PERSON>ve configurada", "optimized_version": "Versão otimizada, 50% mais rápida e econômica", "robust_version": "<PERSON><PERSON><PERSON> robusta, com raciocínio mais complexo", "try_ask_again": "Tente perguntar novamente.", "last_updated_entries": "Cadastros atualizados por último", "update_now": "Atual<PERSON>r agora", "analysis_model_want_to_use": "Agora selecione o modelo de análise que deseja utilizar:", "start_conversation": "Iniciar conversa", "hi_i_am_bia": "<PERSON><PERSON><PERSON>, eu sou a AI Copilot!", "hi_i_am_lila": "<PERSON><PERSON><PERSON>, eu sou a Lila!", "hi_i_am_ai": "<PERSON><PERSON><PERSON>, eu sou a {name}!", "you_are_in_mode": "Você está no modo", "quick_analysis": "<PERSON><PERSON><PERSON><PERSON>.", "advanced_analysis": "<PERSON><PERSON><PERSON><PERSON>.", "i_am_trained_respond_based_on": "Eu sou treinada para responder com base nos dados disponíveis", "this_skill": "nesta skill", "this_project": "Neste projeto", "which_are": "que são:", "i_cannot_perform_calculations_comparisons": "Eu não consigo realizar cálculos e comparativos entre esses dados no modo", "simple_analysis_2": "An<PERSON>lise Simples", "if_want_to_create_more_complex": "caso queira que eu crie indicadores mais complexos e visões com comparativos, crie um novo chat e escolha a", "made_complex_analysis": "feita para análises complexas.", "if_i_am_unable_answer_your_question": "Se eu não conseguir responder sua pergunta, pode ser que os dados solicitados não estejam entre os que tenho disponíveis.", "remember_check_my_answers_using_button": "Lembre-se de verificar minhas respostas usando o botão", "because_ais_still_have_limitation": "pois IAs ainda possuem limitações e podem cometer erros!", "reflection_time_can_2_minutes": "Meu tempo de reflexão pode ser de até 2 minutos, mas consigo responder perguntas complexas desde que elas estejam contidas dentro dos dados que listei acima.", "i_can_answer_your_questions_about": "Consigo te responder perguntas sobre os seguintes indicadores:", "remember_check_answers": "Lembre-se de verificar minhas respostas usando o botão “como cheguei nesse resultado”, pois IAs ainda possuem limitações e podem cometer erros!", "couldnt_find_data": "Oops, não consegui achar os dados que você me pediu.", "have_the_skill'": "Estou com a skill", "have_the_project": "Estou com o projeto", "can_answer_about": "e consigo te responder sobre os seguintes dados:", "and": " e ", "you_want_use": "Caso queira utilizar", "create_new_chat": "crie um novo chat!", "another_skill": "outra skill", "another_project": "outro projeto", "remember_that_was_trained_answer": "Lembre-se que eu fui treinada para te responder perguntas sobre seus dados, em breve terei mais", "projects": "projetos", "as_the_ability_to_design": "como capacidade de projetar, correlacionar dados e sugerir ações!", "connect_with_open_ai": "Conecte com a OpenAI", "please_bring_your_own": "Traga sua própria chave da OpenAI para poder utilizar a {name}.", "please_bring_your_own_key": "Conecte sua chave do {model}", "account_settings": "configurações de conta.", "please_bring_your_own_key_tip": "O {platform} utiliza o modelo Gemini Pro 2.5, do Google. Para usar a {aiName}, configure sua chave do Gemini abaixo", "please_bring_your_own_key_tip_2": "— ela será usada por você em todos os seus projetos e workspaces, e pode ser gerenciada nas", "enter_your_key": "Insira sua chave", "requirements_tier_1": "Requisito: A chave deve ser de nível Tier 1", "connect": "Conectar", "get_your_key": "Para pegar sua chave, acesse o site da", "get_your_key_2": "Para pegar sua chave, acesse o site {preposition_article}", "view": "Visualizar", "table": "tabela", "graphic": "grá<PERSON>o", "use_own_key": "<PERSON><PERSON><PERSON><PERSON> minha chave", "full_screen": "em tela cheia", "create_dashboards_seconds": "Crie Dashboards em segundos", "ai_m1": "<PERSON>lisando sua demanda...", "ai_building_plan": "Construindo plano...", "executing_interaction": "Executando plano...", "ai_m2": "Preparando consulta...", "ai_m3": "Gerando insights adicionais...", "ai_m4": "Quase tudo pronto...", "ai_m5": "<PERSON><PERSON><PERSON> textual...", "ai_m6": "Vou demorar um pouco mais pois estou checando a consulta que eu fiz...", "ai_m7": "Nenhum dado foi encontrado para a sua consulta, então vou tentar de novo...", "ai_m8": "Estou fazendo uma busca mais aprofundada e isso pode demorar um pouco...", "new_name": "Novo nome", "ai_mode_builder_hint": "Constrói sistemas do 0, incluindo tabelas, processos, telas e ações. Demora mais para responder.", "ai_mode_analyst_hint": "A partir do sistema criado, consegue construir anális<PERSON>, tem maior velocidade.", "create_chat_start_conversation": "Crie um chat para iniciar uma conversa", "last_updated": "Ultima atualização", "bia_settings": "Configurações AI Copilot", "lila_settings": "Configurações Lila", "oops_looks_like_youre_out_credits": "Ops! Parece que você está sem créditos.", "out_credits_descr": "Adicione créditos para continuar usando a Lila e acessar todos os recursos disponíveis.", "credits": "C<PERSON>dit<PERSON>", "insert_key_instruction": "Insira sua chave para continuar usando {article} {tool_name}.", "add_credits": "Adicione c<PERSON>", "to_continue_using_lila": "para continuar usando a Lila.", "add_credits_web_version_lila": "Adicione créditos na versão web para continuar utilizando a Lila.", "acess_settings_pending": "Configurações de acesso pendentes!", "contact_administrator_set_up": "Contate um administrador para configurar.", "run_universal_tool": "Executar plano", "edit_universal_tool": "Propor alterações", "recover_status": "Recuperar Status", "perform_interactions": "Executar interações", "additional_settings": "Configurações Adicionais", "additional_considerations_for_chatting": "Considerações adicionais para o chat com a", "enter_considerations_here": "<PERSON><PERSON><PERSON> as considerações adicionais aqui", "enter_considerations_here_body_1": "Use este campo para adicionar informações e considerações que serão concatenadas com os prompts dos usuários finais. Essas diretrizes podem ajudar a ", "enter_considerations_here_body_2": "a fornecer respostas mais específicas e relevantes com base nas instruções fornecidas.", "enter_a_value": "Por favor, insira um valor", "minimum_value_is": "O valor mínimo é R$", "mitra_uses": "O Mitra utiliza o modelo ", "by_google": ", fornecido pelo Google.", "connect_key_app": "Conecte sua própria chave de API do Gemini para utilizar a nossa IA", "free_request": "25 requisições gratuitas por dia.", "key_level_required": "Necess<PERSON><PERSON> nível de chave:", "how_works": "Entenda como funciona", "how_get_key": "Como pegar minha chave?", "how_generate_key": "Aqui você vai aprender como gerar sua chave e configurá-la para atender ao requisito de Tier 1.", "how_to_generate_key": "Como gerar sua chave", "enable_tier_1": "Habilite o Nível Tier 1", "problems_gemini_key": "Problemas com sua chave do Gemini", "how_it_works": "Entenda como funciona", "how_to_use": "Saiba como utilizar o Mitra com seu potencial máximo através da IA.", "how_mitra_use_gemini": "Como o Mitra utiliza o Gemini", "mitra_uses_model_intro": "O Mitra utiliza o modelo ", "gemini_pro_2_5": "Gemini Pro 2.5", "provided_by_google": " fornecido pelo Google.", "ensure_autonomy_intro": "Para garantir mais autonomia e escalabilidade, cada usuário conecta sua ", "own_api_key": "própria chave de API", "of_gemini": " do <PERSON>.", "connection_allows_intro": "Esta conexão permite", "requests_per_day": "25 requisições por dia", "on_gemini_pro_2_5": ", para novas contas, acesso aos ", "free_credits_amount": "$300 em créditos gratuitos", "from_google_valid_for_90_days": " do <PERSON>, válidos por 90 dias.", "plans_how_it_applies_title": "Como isso se aplica nos planos da Mitra:", "plan_free_label": "Plano Free", "plan_free_details_part1": " — Acesso ao ambiente full stack com banco de dados e construtor de páginas, com ", "plan_free_requests": "10 requisições de IA por dia", "plan_free_details_part2": ". O valor da nuvem é bancado por nós.", "plan_starter_label": "<PERSON><PERSON> Starter", "plan_starter_details_part1": " — ", "plan_starter_unlimited_ai": "Uso ilimitado da IA na nossa plataforma*", "plan_starter_details_part2": " + publicação Web com marca própria e apps nas lojas da Apple e Google.", "simple_logic_emoji": "💡 ", "simple_logic_title": "A lógica é simples:", "simple_logic_explanation_text": " você traz a energia (a chave do Gemini), e o Mitra fornece a estrutura para transformar isso em aplicações web e mobile completas.", "paid_plan_gemini_limit_note": "* Mesmo com plano pago, a chave da API do Gemini que você está usando pode ter limites próprios definidos pelo Google, como cota diária ou mensal.", "access_the_intro": "Acesse o ", "google_ai_studio_link_text": "Google AI Studio", "login_with_google_account_part1": " e faça login com sua conta Google. Após o login, clique em ", "get_api_key_button_text": "Get API Key", "login_with_google_account_part2": ". <PERSON>, clique em ", "create_api_key_button_text": "Criar chave de API", "key_generation_instructions_part1": ". A chave será gerada automaticamente e você poderá copiá-la. Basta clicar em ", "copy_button_text": "Copy", "key_generation_instructions_part2": " para copiar a chave e colá-la onde for necessário.", "to_access_key_intro": "Para acessar a chave do ", "tier_1_level_text": "Nível Tier 1", "in_gemini_access_intro": " no <PERSON>, acesse ", "configure_billing_text": "Configurar faturamento", "in_section_intro": " na seção ", "api_plan_billing_section_text": "API Plan Billing", "and_register_payment_method_part1": " e cadastre um meio de pagamento. Os dados são usados apenas para verificar sua identidade — ", "no_automatic_charges_text": "nenhuma cobrança será feita automaticamente", "and_register_payment_method_part2": ". Você só será cobrado se ativar um plano pago ou usar o pré-pagamento. Ao concluir, você recebe ", "free_credit_amount_tier1": "$300 em crédito gratuito", "valid_for_intro": ", válido por ", "valid_for_90_days_text": "90 dias", "remains_available_text": ", que permanece disponível mesmo sem ativar um plano.", "validate_key": "Validar chave", "key_doesnt_fit": "Sua chave não preenche os requisitos do Gemini 2.5. Verifique e tente novamente!", "start_experience_lila": "Comece sua experiência com a nova", "execute_function": "Executar função", "for_ai_work": "Para a nova IA funcionar, nós fizemos uma mudança estrutural nas views geradas automaticamente. Portanto, antes de ativá-la, precisamos executar uma função que retira as descrições das FK's nas CAD_ e CC_, e isso pode exigir manutenção em objetos de banco do seu projeto. Clique no ícone de copiar para verificar se seu projeto precisa de alguma manutenção.", "error_processing_ai_response": "Erro ao processar dados da resposta da IA", "unable_process_ai_response_data": "Não foi possível processar os dados da resposta da IA. Verifique se a resposta está no formato correto.", "upload_files_extensions": "Upload de arquivos (imagens, documentos, código)", "retrying_with_countdown": "Tentativa {1} de {2} ({0}) - Próxima em {3}s", "retrying_processing": "Tentativa {1} de {2} ({0}) - Processando...", "typing_placeholder_1": "Crie um sistema para gerenciar funil de vendas, com oportunidades e contatos...", "typing_placeholder_2": "Crie um fluxo de solicitação e aprovação de férias para funcionários...", "typing_placeholder_3": "Crie um dashboard para visualizar métricas de vendas (KPIs) com gráficos e filtros...", "typing_placeholder_4": "Crie um sistema de gestão financeira para pequenas empresas com fluxo de caixa..."}, "ACTIONS": {"welcome_to_actions": "Bem vindo à Ações!", "select_existing_action": "Selecione uma ação existente ou crie uma nova!", "test_call": "<PERSON><PERSON> chamada", "call_preview": "Preview da chamada:", "call_type": "<PERSON><PERSON><PERSON>", "name": "Nome", "value": "Valor", "i_dont_know_reference": "Não sei a referencia", "preview_answer": "Preview da resposta:", "click_button_to_test_call": "Clique no botão para testar a chamada e \n gerar o preview da resposta.", "there_were_no_headers": "Não houve headers c<PERSON>os.", "request_without_body": "Requisição sem body.", "with_con": "Com cron", "without_con": "Sem cron"}, "TIME": {"time_now": "<PERSON><PERSON><PERSON>", "time_today": "Hoje", "time_yesterday": "Ontem", "time_before_day_yesterday": "<PERSON><PERSON><PERSON><PERSON>", "time_3_days_ago": "3 dias atrás", "time_last_7": "Últimos 7 dias", "time_last_15": "Últimos 15 dias", "time_last_30": "Últimos 30 dias", "time_last_month": "<PERSON><PERSON><PERSON> passado", "time_more_60_than_days": "Mais de 60 dias atrás", "time_ago_specific": "Há {diffInDays} dias", "time_january": "Janeiro", "time_february": "<PERSON><PERSON>", "time_march": "Março", "time_april": "Abril", "time_may": "<PERSON><PERSON>", "time_june": "<PERSON><PERSON>", "time_july": "<PERSON><PERSON>", "time_august": "Agosto", "time_september": "Setembro", "time_october": "Out<PERSON>ro", "time_november": "Novembro", "time_december": "Dezembro", "time_last_year": "Ano passado"}, "dates": {"today": "Hoje", "yesterday": "Ontem", "last_7_days": "Últimos 7 dias", "last_30_days": "Últimos 30 dias"}, "months": {"january": "Janeiro", "february": "<PERSON><PERSON>", "march": "Março", "april": "Abril", "may": "<PERSON><PERSON>", "june": "<PERSON><PERSON>", "july": "<PERSON><PERSON>", "august": "Agosto", "september": "Setembro", "october": "Out<PERSON>ro", "november": "Novembro", "december": "Dezembro"}, "MEMBERS": {"invite_business_user": "Publique o projeto antes de convidar um usuário Business.", "invite_new_user": "Convidar Novo Usuário", "share_link": "Copie o link abaixo para compartilhá-lo com o usuário:", "invite_success": "Convite gerado com sucesso!", "coming_soon": "Em breve", "notify_user_email": "Notificar o usuário por e-mail sobre este convite", "user": "usuário | usuários", "members": "Membros | Membro", "invite_members": "Convidar membros para o projeto", "invite_workspace_members": "Convidar membros para o workspace", "project_members": "Membros do Projeto", "workspace_members": "Membros do workspace", "search_member": "Pesquisar um membro", "base_access": "Acesso pelo projeto", "workspace_access": "Acesso pelo workspace", "inivitation_sent": "Convite enviado", "members_access": "Membros & Acessos", "members_access_subtitle": "Gerencie os membros do seu projeto e as permissões de suas contas.", "corporative_security": "Segurança corporativa", "add_corporative_security": "Adicionar segurança corporativa", "new_member": "Novo Membro", "delete_corporative_security": "Deletar segurança corporativa?", "delete_corp_body_1": "Tem certeza que deseja deletar a segurança corporativa", "delete_corp_body_2": "Essa ação não pode ser desfeita.", "delete_member_title": "Tem certeza que deseja excluir esse membro?", "delete_member_body": "Essa ação não podera ser desfeita.", "screens": "Telas", "no_description": "Sem descrição", "profiles": "<PERSON><PERSON><PERSON>", "data": "<PERSON><PERSON>", "edit_profile": "<PERSON><PERSON> perfil", "create_profile": "<PERSON><PERSON><PERSON> perfil", "group": "Grupos", "PROFILES": {"title": "<PERSON><PERSON><PERSON>", "new": "Novo perfil"}, "action": "Ação", "dont_have_profile_yet": "Você ainda não possui perfil", "create_profile_different_types_users": "Crie perfis para dar acessos personalizados a diferentes tipos de usuários.", "tiny_profile": "perfis"}, "HELP": {"help": "<PERSON><PERSON><PERSON>", "search": "Busca", "what_is_your_doubt": "Qual é a sua dúvida?", "how_to_use_template_package": "Como usar o pacote de template", "learn_with_our_step_by_step_guide": "Aprenda com o nosso guia passo a passo", "learn_by_doing_together": "Aprenda fazendo junto", "learn_by_use_cases": "Aprenda por casos de uso", "explore_with_practical_examples_solved_in_mitra": "Explore com exemplos práticos resolvidos no Mitra", "faq": "D<PERSON>vid<PERSON>e<PERSON>", "check_out_our_help_playlist_on_youtube": "Confira a nossa playlist de ajuda no Youtube", "related_content": "Conteúdos relacionados", "our_community": "Acesse nossa comunidade no Discord", "check_our_docs": "Confira nossa documentação completa"}, "NATIVE": {"natives": "Nativas", "cubes": "<PERSON><PERSON><PERSON>", "dimensions": "Dimensões", "connections": "Conexões", "actions": "Ações", "connectors": "Conectores", "details_modal": "<PERSON><PERSON>", "forms": "Formulários", "users": "Usuários", "mask": "Máscara", "identity": "Identidade", "executables": "Executáveis"}, "PAGEBUILDER": {"update_excluded_html": "Lista dos IDs de componentes que você não quer que sejam atualizados.", "update_update_html": "Lista dos IDs de componentes que você quer atualizar.", "update_all_html": "Se true, atualiza todos os componentes da tela.", "query_variable": "variável de query", "for_modal_action": "para mostrar modal de ação", "variable_reactivity": "Atualizar componentes listados na reatividade manual", "select_existing_item": "Selecione ou crie novas telas web, \n telas mobile e formulários!", "welcome_to": "<PERSON><PERSON> vindo à", "search_options": "Pesquisar opção", "nickname": "Apelido", "dimension": "<PERSON><PERSON><PERSON>ão", "save_editions": "<PERSON><PERSON>", "edition": "Edição", "document": "Documento", "native_codes": "Codigos Nativos", "log_of_entire_object": "//log do objeto todo", "log_of_entire_view": "//log da view inteira", "log_of_first_row": "//log da primeira linha", "log_of_first_item": "//log do primeiro item da primeira linha", "component_view_matrix": "<PERSON><PERSON> da <PERSON> do componente", "action_id": "Id da action", "for_floating_modal": "para modal flutuante", "to_close_reload": "para fechar ao recarregar", "source_line_index_selection_interactions": "index da linha do source, para aplicar seleção nas interações", "modal_without_applying": "//modal sem aplicar seleção.", "modal_with_list_row": "modal com seleção da linha da lista", "modal_where_index_is_source": "modal em que index é a linha do source (0 é alinha 1)", "same_call_above_mandatory": "mesma chamada de cima, mas apenas com os atributos obrigatórios.", "content_id_record": "contentId é o ID do cadastro que quer selecionar", "detail_index_source": "//detail em que index é a linha do source (0 é alinha 1).", "detail_id_record": "//detail com base no ID do cadastro que quer editar.", "action_without_applyng": "//action sem aplicar seleção.", "action_where_index": "//action em que index é a linha do source (0 é alinha 1).", "immediately_call_code": "chama imediatamente o proximo codigo.", "calls_return_action_mitra": "chama no retorno de actionMitra.", "or": "ou", "call_next_code": "chama proximo codigo quando actionMitra finalizar.", "addition_form": "//form de adição.", "editing_form_index": "//form de edição em que index é a linha do source (0 é alinha 1).", "editing_form_id_registration": "//form de edição com base no ID do cadastro que quer editar.", "return": "//retorna", "created_or_changed": "que foi criado ou alterado, ou null se o modal apenas foi fechado.", "call_return_form_mitra": "chama no retorno de formMitra.", "call_form_mitra_finishes": "chama proximo codigo quando formMitra finalizar.", "db_action_without_selection": "//dbAction sem aplicar seleção.", "db_action_index": "//dbAction em que index é a linha do source (0 é alinha 1).", "calls_return_db_action": "chama no retorno de dbactionMitra.", "call_next_code_db_action": "chama proximo codigo quando dbactionMitra finalizar.", "variable_name": "<PERSON>me da variavel", "variable_content": "<PERSON><PERSON><PERSON> varia<PERSON>", "call_next_code_set_variable": "chama proximo codigo quando setVariableMitra finalizar.", "calls_return_set_variable_mitra": "chama no retorno de setVariableMitra.", "returns_query_value": "//retorna o valor da consulta.", "call_next_code_query_mitra": "chama proximo codigo quando queryMitra finalizar.", "calls_return_query_mitra": "chama no retorno de queryMitra.", "update_mitra_components": "Consegue pedir para atualizar todos os componentes na tela, ou apenas algum componentes cujo ID for informado. Se o componente tiver a função 'updateMitra' ao invez de recriar o componente, ele chama o metodo.", "add_method_descr": "Precisa adicionar o metodo com esse nome 'updateMitra' na raiz do script para funcionar, isso faz com que invez do componente atualizar, ele chama esse metodo. serve para controlar manualmente os updates, e evitar piscar, ou cair no estado inicial novamente.", "when_mitra_updates_descr": "quando o componenteMitra atualizar, ao invéz de atualizar o componente todo, ele vai chamar esse metodo apenas.", "upload_mitra_descr": "O método salva o arquivo no servidor, e retorna o caminho do arquivo salvo.", "upload_att_descr": "Arquivo a ser enviado para o drive do Mitra."}, "ACTION": {"step_refresh_screen": "<PERSON><PERSON><PERSON><PERSON>", "step_go_to_screen": "Ir pra tela", "step_clear_cube": "Limpar atributo", "step_go_to_module": "Ir pro módulo", "step_go_to_group": "Ir pro grupo", "step_conditional": "Condicional", "step_save_data_entry": "<PERSON>var entrada de dados", "step_backup": "Backup", "step_reset_backup": "Restaurar backup", "step_wait": "<PERSON><PERSON><PERSON>", "step_show_message": "Mostrar mensagem", "step_call_action": "<PERSON><PERSON>", "step_selection": "Filtrar", "step_apply_selection": "Aplicar filtro na tela atual", "step_reset_selection": "Redefinir todos filtros", "step_reset_tree_selections": "Redefinir filtros na árvore", "step_selection_interactivity": "filtro interativo", "step_selection_cube": "Filtrar com atributo", "step_selection_save": "<PERSON><PERSON> filtro atual", "step_selection_recovery": "Recuperar filtro", "step_data_flow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "step_clean_dimension": "Limpar cadastro", "step_tree_reader": "Tree reader (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "step_cube_reader": "Cube reader (De<PERSON><PERSON>ciado)", "step_extract_tree": "Extrair árvore", "step_extract_dimension": "Extrair cadastro", "step_extract_cube": "Extrair atributo", "step_extract_all_dimensions": "Extrair todas os cadastros", "step_extract_all_cubes": "Extrair todos os atributos", "step_extract_source": "Extrair view", "step_send_emails": "Enviar E-mails (Depreciado)", "step_selection_by_cube": "Filtrar por atributo", "step_write_back": "Escrever de volta", "step_export_pdf": "Exportar para PDF", "step_execute_jar": "Executar JAR", "step_change_relationship": "Alterar FK", "step_recalculate_automation": "Recalcular automação", "step_run_connection": "<PERSON><PERSON>", "step_edit_register": "Criar/Alterar registro", "step_send_emails_migration": "Enviar E-mails", "step_send_emails_migration_call_api": "Chamar API", "step_logout": "Deslogar usuá<PERSON>", "step_download_pdf": "Baixar PDF", "step_end_action": "Finalizar Ação", "need_chips": "É preciso colocar os chips, para poder criar / alterar registros", "create_action": "Criar ação", "action_name": "Nome da ação", "group_name": "Nome do grupo", "step": "Passo", "called_step": "<PERSON><PERSON>", "query_result": "<PERSON>sul<PERSON><PERSON> da <PERSON>a"}, "DETAILBUILDER": {"detail_modal": "<PERSON><PERSON>", "delete_form": "<PERSON><PERSON><PERSON>", "delete_form_body_1": "Tem certeza que deseja deletar o formulário", "delete_form_body_2": "Essa ação não pode ser desfeita e todos os dados referentes ao formulário serão deletados!", "delete_detail_body_1": "Tem certeza que deseja deletar o detail", "delete_detail_body_2": "Essa ação não pode ser desfeita e todos os dados referentes ao detail serão deletados!", "delete_detail": "Deletar Detail", "delete_action_body_1": "Tem certeza que deseja deletar a ação", "delete_action_body_2": "Essa ação não pode ser desfeita e todos os dados referentes a ação serão deletados!", "delete_action": "Deletar Ação", "username": "Nome do usuario", "field_config": "Configurar campo", "field_name": "Nome do campo", "new_button": "Novo botão", "view": "Visualizar", "settings": "Configurações", "add_field": "Adicionar campo", "fields": "Campos", "choose_type_content_to_create": "Escolha o tipo de conteúdo que deseja criar:", "screen": "Tela", "form": "<PERSON><PERSON><PERSON><PERSON>", "new_field": "Novo Campo", "edit_field": "<PERSON><PERSON>", "add_button": "<PERSON><PERSON><PERSON><PERSON>", "edit_button": "<PERSON><PERSON>", "personalize": "Personalizar", "title": "<PERSON><PERSON><PERSON><PERSON>", "insert": "Inserir", "tip": "Dica", "blocking_criteria": "Crit<PERSON><PERSON>", "select": "Selecionar", "type_of_execution": "Tipo de execução", "close_modal": "Fechar modal ao recarregar", "float_modal": "Modal flutuante", "close_details_modal": "Fechar modal de detalhes ao recarregar", "delete_button": "<PERSON><PERSON><PERSON>", "win": "Ganhar", "complete": "Concluir", "lose": "<PERSON><PERSON>", "delete": "Deletar", "file": "<PERSON><PERSON><PERSON><PERSON>", "stand_by": "Espera", "icon": "Ícone", "background_color": "Cor do fundo", "text_icon_color": "Cor do texto/ícone", "action_executor": "Executor de ação", "open_screen": "<PERSON><PERSON><PERSON> tela", "action": "Ação", "step_edit_register": "Criar/Alterar registro", "edit_view_query": "Configurar SQL", "module": "<PERSON><PERSON><PERSON><PERSON>", "height": "Altura", "width": "<PERSON><PERSON><PERSON>", "show_screen_popup": "Mostrar tela como popup", "no_data_available": "Nenhum dado disponível", "modal_height": "Altura do modal", "modal_width": "Largura do modal", "delete_field": "Deletar campo", "delete_field_title": "Deletar Campo?", "delete_field_body": "Essa ação vai impactar diretamente no Database e não poderá ser desfeita. Tem certeza que deseja deletar?", "tab_name": "Nome da aba", "timeline": {"create_timeline": "Criar Timeline", "tab": "Aba", "tab_timeline": "(Timeline) ", "new_tab": "Nova Aba", "card_title": "Título do <PERSON>", "username": "Nome do Usuário", "creation_date": "Data de Criação", "card_description": "Descrição do card", "description": "Descrição", "other_card_fields": "Outros campos", "image": "Foto", "drag_desired_fields": "<PERSON><PERSON>ste os campos desejados", "query_not_found": "Ainda não há nenhum SQL configurado.", "query_not_found__subtitle": "Para visualizar a Timeline, é necessário configurar um SQL primeiro.", "buttons": "<PERSON><PERSON><PERSON><PERSON>", "empty_title": "Dados não mapeados.", "empty_subtitle": "O SQL está configurado, mas não possui vínculo com nenhum cadastro.", "empty_subtitle_note": "Certifique-se de marcar a coluna ID do cadastro para tudo funcionar corretamente."}, "screen_flap": {"create_screen": "C<PERSON>r Tela", "change_screen": "Alterar tela", "change_screen_body": "Para alterar a tela escolhida selecione outra opção nos campos abaixo:"}, "form_flap": {"create_form": "<PERSON><PERSON><PERSON>", "create_detail": "<PERSON><PERSON><PERSON>", "change_form": "Alterar tela", "change_form_body": "Para alterar o formulário escolhido selecione outra opção no campo abaixo:"}, "delete_flap": "Apagar Aba", "item_of": "<PERSON><PERSON> de ", "edit_details_modal": "<PERSON><PERSON>", "standard_color": "<PERSON><PERSON>", "blocking_message": "Mensagem do bloqueio", "delete_details_modal": "<PERSON><PERSON><PERSON>", "default_button": "Botão <PERSON>", "delete_details_modal_title": "<PERSON><PERSON><PERSON>?", "delete_details_modal_body": "Tem certeza que deseja deletar o modal de detalhes? Essa ação não poderá ser desfeita.", "delete_details_flap_body": "Tem certeza que deseja deletar a aba do modal de detalhes? Essa ação não poderá ser desfeita.", "delete_details_button_body": "Tem certeza que deseja deletar o botão do modal de detalhes? Essa ação não poderá ser desfeita.", "confirme_message": "Mensagem Confirmação", "message": "Mensagem", "accept_button_text": "Texto botão \"Aceitar\"", "cancel_button_text": "Texto botão \"Cancelar\"", "configure_filters": "<PERSON><PERSON><PERSON><PERSON>", "configure_form": "<PERSON><PERSON><PERSON><PERSON>", "enable_card_deletion": "Permitir del<PERSON>ção de cards", "edit_form": "Formulário de edição", "addition_form": "Formulário de adição", "currency_type": "<PERSON><PERSON><PERSON>", "decimal_digits": "<PERSON><PERSON><PERSON><PERSON>", "user_creation": "Usu<PERSON><PERSON>", "creation_date": "Data Criação", "description": "Descrição", "mask": {"time": "Tempo", "none": "<PERSON><PERSON><PERSON><PERSON>", "telephone": "Telefone"}, "edit_filter": "<PERSON><PERSON> filtro", "register": "Cadastro", "static": "Estática", "dynamic": "Dinâmica", "timeline_fields": "Campos Timeline", "there_no_sql_yet": "Ainda não há nenhum SQL configurado. ", "to_view_timeline_need_sql": "Para visualizar a Timeline, é necessário configurar um SQL primeiro.", "enable_mobile_version": "Ativar versão mobile", "web_screen": "Tela Web", "blank_card": "Card em branco.", "white_card_description_1": "O card está em branco, pois nenhum valor ou botão foi definido.", "white_card_description_2": "Configure os campos para que o card seja preenchido.", "none_active_buttons": "Nenhum botão foi adicionado até o momento."}, "API": {"save_error": "Falha ao salvar!", "auth_error": "Ops, estamos em manutenção. Tente novamente em breve, retornaremos rapidamente."}, "TABLE_ADD_MENU": {"default_title": "Criar Nova Tabela", "column_headers": {"type": "Tipo", "default_value": "<PERSON><PERSON>", "primary": "Primária", "unique": "Unique", "required": "Required"}, "placeholders": {"column_name": "nome_da_coluna", "select_type": "Selecione o tipo", "null": "NULL"}, "add_column": "<PERSON><PERSON><PERSON><PERSON>", "foreign_key_section_title": "Conexão com outra tabela (Foreign key)", "relation_with": "Relação com:", "add_foreign_key_connection": "Adicionar <PERSON> com outra tabela", "create_table_action_fallback": "<PERSON><PERSON><PERSON>"}, "CONNECTION": {"data_source_for_ai": "Fonte de dados que será utilizada pela IA.", "true": "<PERSON>m", "false": "Não", "null": "<PERSON><PERSON><PERSON>", "applied_filters": "Filtros Aplicados", "variable_list": "Lista de variáveis", "variables": "Variáveis", "variable": "Variável", "value": "Valor", "connection": "Conexão", "create_jdbc": "<PERSON><PERSON><PERSON>", "edit_jdbc": "<PERSON><PERSON>", "query": "Query", "empty_query": "Inserir query", "query_error": "Falha ao executar query, verifique se a query ou JDBC estão corretos", "delete_connector": "Excluir conector", "run_query": "<PERSON><PERSON> query", "connector_edit": "<PERSON><PERSON> conector", "start_connection": "<PERSON><PERSON><PERSON>", "connection_init": "Como deseja iniciar?", "type_of_data": "Tipo do dado", "type_of_connection": "Tipo de <PERSON>", "insert_connection_name": "Insira o nome da conexão", "name": "Nome", "dimension": "Cadastro", "select_or_create_dimension": "Selecione ou crie um cadastro", "dimension_name": "Nome do cadastro", "group": "Grupo", "delete_connection_body_1": "Tem certeza que deseja deletar a conexão", "delete_connection_body_2": "Essa ação não pode ser desfeita e todos os dados referentes a conexão serão deletados!", "select_group": "Selecione ou crie", "insert": "Inserir", "replace": "Substituir", "separator": "Separador", "separator_mi": "Separador de milhar", "use_this_as_calendar": "Usar como calendário", "calendar_default_name": "<PERSON><PERSON><PERSON><PERSON>", "select_dimension": "Selecionar Cadastro", "select_chip": "Selecionar", "add_new": "<PERSON><PERSON><PERSON>", "rename": "Renomear", "path_to_file": "Caminho para o arquivo", "no_dimension_found": "<PERSON>enhum dado encontrado", "no_cube_found": "<PERSON>enhum dado encontrado", "no_data_found": "Sem dados", "no_data_found_description_p1_cube": "Importe um CSV", "no_data_found_description_p2_cube": " ou defina o caminho para iniciar.", "no_data_found_description_p3_sql": "Termine de configurar e escrever a query", "select_cube": "Selecionar Cubo", "settings_data": "Configuração da data", "inferid_data": "Data reconhecida", "date_format": "Formato", "csv_connection": "Conexão", "switch_data_source": "Editar fonte de dados", "what_the_connection_type": "Qual será o tipo da conexão?", "insert_configure_worksheet": "Insira e configure a planilha", "check_fields": "Verifique os campos", "insert_worksheet_configure_path_view_data": "Insira a planilha ou configure o caminho para visualizar os dados.", "insert_query_configure_path_view_data": "Insira a query para visualizar os dados.", "insert_query_configure_timeline": "Insira a query e associe as colunas para configurar a timeline", "check_fields_body": "Para concluir, é necessário que todos os campos abaixo sejam definidos na planilha ✅. É importante que a ordem também seja a mesma.", "configure_JDBC": "Configure o JDBC", "configure_and_run_query": "Configure e execute a query", "execute": "Executar", "inactive": "Inativo", "loading": "Carregando", "preview": "Pré-visualização", "resume": "Resumo", "templates": "Templates", "quick_consultation": "Consulta rápida ", "quick_consultation_body": "Faça sua busca de forma ágil utilizando um dos templates abaixo:", "change": "Trocar", "define_search_arguments": "Defina os argumentos da busca:", "select_all": "Selecionar todos", "start_with_template": "Iniciar com Template", "remove": "Remover", "new_dimension": "Novo cadastro", "schedule": "Agendamento (Cron)", "schedule_invalid": "Agendamento (Cron) inválido", "generator": "G<PERSON>dor", "connection_log": "Log conex<PERSON>", "run_by": "Executado por", "date_and_time": "Data e hora", "summary": "Resumo", "success": "Sucesso | Sucessos", "partial_success": "Sucesso parcial | Sucessos parciais", "failure": "Falha | Falhas", "failed": "Fal<PERSON>", "search": "Pesquisar...", "connection_not_have_logs": "Essa conexão não possui nenhum registro de log no momento.", "log_summary": "Resumo log", "detailing": "Detalhamento", "cleaning_the_cubes": "Limpeza dos cubos", "structure": "Estrutura", "accepted_lines": "<PERSON><PERSON>", "rejected_lines": "<PERSON><PERSON>", "reason_for_rejections": "Motivo rejeições", "uploader_type": ".CSV aqui", "description": "Descrição", "enter_the_connection_name": "Insira o nome da conexão", "cron_expression_generator": "Gerador de expressão de cron", "active": "Ativo", "enlarged_view": "Visualização ampliada", "connection_name": "<PERSON><PERSON> da <PERSON>", "port": "Porta", "database": "Banco de dados", "user": "<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON>", "field_required": "Esse campo é obrigatório", "authentication_failure": "<PERSON><PERSON><PERSON><PERSON> ou senha incorretos.", "user_does_not_have_access": "Este usuário não possui permissão para acessar esse JDBC.", "no_results_found": "Nenhum resultado encontrado", "this_screen_is_preview_mode": "Essa tela está em modo visualização.", "connection_dimension_deleted": "O cadastro vinculado a esta conexão foi deletado!", "last_run_of_connection_failed": "A última execução dessa conexão teve falhas!", "when_editing_data_source_review_fild_formats": "Ao editar a fonte de dados revise os formatos dos campos.", "check_faults_here": "Verificar falhas aqui", "delete_connection": "<PERSON><PERSON><PERSON>", "delete_connection_body": "Essa ação não poderá ser desfeita. Tem certeza que deseja deletar?", "break_bond": "Quebrar v<PERSON>", "execution_time": "Tempo de execução", "something_went_wrong": "<PERSON>go deu errado", "connection_could_not_established": "A conexão não pôde ser estabelecida, pois o cadastro", "requires_a_field": "requer um campo", "which_not_provided_when_uploading": "que não foi fornecido no upload da planilha.", "please_check_the_spreadsheet": "Por favor, verifique a planilha e assegure-se de incluir o campo obrigatório", "before_trying_again": "antes de tentar novamente", "failed_execute_query": "Falha na execução do carregamento.", "unexpected_error_execute_query": "Ocorreu um problema inesperado durante a execução do carregamento dessa conexão.", "error_detail": "Detalhamento de erro:", "confirmation": "Confirmação", "here_below_structures_review": "Confira as estruturas que serão criadas ou populadas com essa conexão:", "connection_registration_of": "Atributos de ", "origin": "Origem", "id_type": "Tipo ID", "type": "Tipo", "new": "Novo!", "native": "Nativo", "existent": "Existente", "change_key": "<PERSON>ve da alteração", "affected_members_will_be_new": "Todos os membros afetados serão novos", "change_existing_members": "Farei alterações em membros já existentes utilizando 'Descrição' do membro como chave", "description_of": "Descrição de ", "id_of": "ID de ", "select_connection_type": "Selecione o tipo de conexão", "data_source": "Fonte de dados", "connections": "Carregamentos", "switch_to_sql": "Mudar para SQL", "switch_to_csv": "Mudar para CSV", "import_from_external_database": "Importar de banco externo", "scheduling": "Agendamento", "without_scheduling": "Sem agendamento", "run_now": "Executar agora", "executed_there": "Executado", "executing": "Em andamento", "execution_failures": "Falhas de Execução", "failed_execute": "Falha na execução", "columns": "Colunas", "new_columns": "Tipo", "setting": "Configuração", "numeric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "Texto", "date": "Data", "descr_other_column": "Descrição de outra coluna", "descr_of_fk": "Descrição de uma FK", "should_become_description": "deve se tornar descrição de", "choose_column": "Escolha a coluna", "day": "<PERSON>a", "view_log": "Ver log", "table": "<PERSON><PERSON><PERSON>", "cube": "<PERSON><PERSON><PERSON>", "new_column": "Nova coluna", "existing_column": "Coluna existente", "new_connection": "Novo Carregamento", "connection_type": "Tipo de Carregamento", "connection_2": "Carregamento", "connection_name_2": "Nome do carregamento", "import_from_eip": "Importar do EIP", "create_empty_table": "<PERSON><PERSON><PERSON> tabela vazia", "you_havent_uploaded_yet": "Você ainda não fez um carregamento", "will_all_rows_be_new": "<PERSON><PERSON> as linhas serão novas na tabela?", "yes_all_lines_will_be_new": "<PERSON><PERSON>, to<PERSON> as linhas serão novas", "no_use_id_to_update_existing": "Não, utilize o ID para atualizar registros já existentes", "no_use_description_to_update_existing": "Não, utilize a descrição para atualizar registros já existentes", "using_fks_as_replacement_key": "Utilizar uma das FKs como chave de substituição", "clean_cubes_before_loading": "Limpar os cubos antes de carregar", "clear_files_day_range": "Limpar o range de dias do arquivo antes de carregar", "clear_query_day_range": "Limpar o range de dias da query antes de carregar", "save_editions": "<PERSON><PERSON>", "save_and_run": "Salvar e Executar", "total_values": "Totalizar valores", "data_source_name": "Nome da fonte de dados"}, "SHEET": {"DYNAMIZATION": {"title": "Dinamização", "edit_dynamization": "<PERSON><PERSON>", "create_dynamization": "<PERSON><PERSON><PERSON>"}, "CONTEXT_MENU": {"cut_out": "Recortar", "copy": "Copiar", "paste": "Colar", "insert_row_below": "Inserir 1 linha abaixo | Inserir {count} linhas abaixo", "insert_row_above": "Inserir 1 linha acima | Inserir {count} linhas acima", "insert_column_left": "Inserir 1 coluna à esquerda | Inserir {count} colunas à esquerda", "insert_column_right": "Inserir 1 coluna à direita | Inserir {count} colunas à direita", "unfreeze_rows": "<PERSON><PERSON><PERSON><PERSON> lin<PERSON>", "unfreeze_columns": "<PERSON><PERSON><PERSON><PERSON> colu<PERSON>", "insert_cells": "<PERSON><PERSON><PERSON>", "insert_cells_and_shift_right": "Inserir células e deslocar para a direita", "insert_cells_and_shift_down": "Inserir células e deslocar para baixo", "delete_cells_and_shift_left": "Excluir células e deslocar para a esquerda", "delete_cells_and_shift_up": "Excluir células e deslocar para cima", "insert_copied_cut": "Inserir copiado ou cortado", "delete_line": "Excluir linha | Excluir linhas", "delete_selected_lines": "Excluir linhas selecionadas", "delete_selected_columns": "Excluir colunas selecionadas", "clear_selected_columns": "Limpar colunas selecionadas", "clear_selected_lines": "Limpar colunas selecionadas", "delete_column": "Excluir coluna | Excluir colunas", "delete_cells": "Excluir <PERSON>", "transform_to_dynamic_cell": "Transformar em célula dinâmica", "see_more_cell_actions": "<PERSON>er mais açõ<PERSON> da célula", "conditional_formatting": "Formatação condicional", "display_column": "<PERSON><PERSON><PERSON> coluna", "sort_A_to_Z": "Classificar de A a Z", "sort_Z_to_A": "Classificar de Z a A", "see_more_column_actions": "<PERSON>er mais açõ<PERSON> da coluna", "freeze_up_to_column": "<PERSON><PERSON><PERSON> até a coluna", "freeze_up_to_row": "<PERSON><PERSON><PERSON> até a linha", "group_column": "Agrupar colunas", "clear_column": "Limpar coluna | Limpar colunas", "hide_column": "Ocultar coluna | Ocultar colunas", "clear_line": "Limpar linha | Limpar linhas", "hide_line": "Ocultar linha | Ocultar linhas", "show_row": "<PERSON><PERSON><PERSON>", "see_more_line_actions": "<PERSON>er mais aç<PERSON> da linha", "group_line": "Agrupar linhas", "delete": "Excluir", "hide": "Ocultar", "move_right": "Mover para direita", "move_left": "Mover para esquerda"}, "VIEW": {"edit_data": "editar dado", "show_all": "<PERSON><PERSON> todos", "block_as_line": "Bloco como linha", "filters": "<PERSON><PERSON><PERSON>", "no_created_filter": "Nenhum filtro foi criado", "add_lines": "Adicionar em linha", "select_below": "Selecione um dado abaixo", "add_column": "Adicionar em coluna", "disabled_range": "Desabilitamos o range automático para aplicar a fórmula", "automatic_range": "Range automático", "add_in_data": "Adicionar em dados", "data_not_be_metric": "Esse tipo de dado não pode ser adicionado como métrica", "data_only_be_metric": "Dado tipo {type} só pode ser adicionado como métrica"}, "RANGE": {"area_range": "Intervalo da tabela", "select_range": "Selecione um intervalo", "invalid_range": "Range Inválido"}, "FORMULAS": {"sum": "<PERSON><PERSON>", "average": "Média", "count_numbers": "<PERSON><PERSON>", "maximum": "Máximo", "minimum": "<PERSON><PERSON><PERSON>"}, "FORMATTER": {"undo": "Desfazer (Ctrl + Z)", "redo": "Refazer (Ctrl + Y)", "format_as_currency": "Formatar como moeda", "format_as_percentage": "Formatar como porcentagem", "decrease_number_decimal_places": "Di<PERSON><PERSON>r númer<PERSON> de casas decimais", "increase_number_decimal_places": "Aumentar número de casas decimais", "more_formats": "<PERSON><PERSON> formatos", "formatting": "Formatação", "font_size": "<PERSON><PERSON><PERSON>", "simple_text": "Texto simples", "bold": "Negrito", "italic": "Itálico", "text_color": "Cor do texto", "fill_color": "<PERSON><PERSON> <PERSON> pre<PERSON>", "edges": "<PERSON><PERSON><PERSON>", "edge_style": "<PERSON><PERSON><PERSON>", "wrapping": {"title": "Ajuste de texto", "wrap": "Ajustar", "overflow": "<PERSON><PERSON><PERSON><PERSON>", "break": "Cortar"}, "edge_type": {"all": "<PERSON><PERSON> as bordas", "inside": "<PERSON><PERSON><PERSON> internas", "horizontal": "<PERSON><PERSON><PERSON>s", "outside": "<PERSON><PERSON><PERSON> externas", "vertical": "<PERSON><PERSON><PERSON> verticais", "top": "Borda superior", "bottom": "Borda inferior", "left": "<PERSON><PERSON>", "right": "<PERSON><PERSON>", "none": "<PERSON><PERSON> bord<PERSON>"}, "color": "Cor", "merge_cells": "Mesclar célu<PERSON>", "align_horizontally": "<PERSON><PERSON>ar na horizontal", "align_vertically": "Alinhar na vertical", "filter": "Filtro", "functions": "Funções", "automatic": "Automático", "number": "Número", "percentage": "Porcentagem", "coin": "<PERSON><PERSON>", "accounting": "Contabilidade", "merge_all": "<PERSON><PERSON><PERSON><PERSON>", "merge_vertically": "Mesclar Verticalmente", "merge_horizontally": "Mesclar Horizontalmente", "unmerge": "<PERSON><PERSON><PERSON> Mesclagem", "left": "E<PERSON>rda", "center": "Centro", "right": "<PERSON><PERSON><PERSON>", "top": "Superior", "middle": "<PERSON><PERSON>", "bottom": "Inferior"}, "sheet_search": "Pesquisar...", "set_pivot_range": "Definir o intervalo de dinamização", "select_all": "Selecionar tudo", "clear": "Limpar", "bars_lines_area": "Barras/Linhas/Área", "graphics": "Grá<PERSON><PERSON>", "dynamization_area": "<PERSON><PERSON><PERSON>", "systematize": "Sistematizar", "column": "coluna", "columns": "colu<PERSON>", "row": "linha", "rows": "linhas", "x_axis": "Eixo X", "dimension": "<PERSON><PERSON><PERSON>ão", "format": "Formato", "select_format": "Selecione um formato", "search": "<PERSON><PERSON><PERSON><PERSON>", "new_metric": "Nova Métrica", "add_cube": "<PERSON><PERSON><PERSON><PERSON>", "table_range": "Intervalo da tabela", "add_registration": "<PERSON><PERSON><PERSON><PERSON>", "please_select_valid_range": "Selecione um intervalo válido para", "view_columns": "visualizar as colunas", "use_description_key": "Usar descrição como chave", "column_2": "Coluna", "columns_2": "Colunas", "mapping": "Mapeamento", "select_data_below": "Selecione um dado abaixo", "configure_view": "Configurar View", "graphics_editor": "Editor de gráficos", "chart_type": "Tipo de gráfico", "elements": "Elementos", "axle": "Eixo", "series_generator_axis": "Eixo gerador de séries", "metrics": "Métricas", "row_2": "<PERSON><PERSON>", "rows_2": "<PERSON><PERSON>", "personalization": "Personalização", "title_text": "Texto do título", "style": "<PERSON><PERSON><PERSON>", "chart_title": "Título do gráfico", "none_selected": "<PERSON><PERSON>hum se<PERSON>", "add_filter": "<PERSON><PERSON><PERSON><PERSON> filtro", "select_all_2": "(Selecionar Todos)", "edit_metric": "<PERSON><PERSON>", "formula": "<PERSON><PERSON><PERSON><PERSON>", "axis_x": "Eixo X", "available_formulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allow_data_entry": "Permit<PERSON> entrada de dados", "complete": "Concluir", "add_data": "<PERSON><PERSON><PERSON><PERSON> dado", "empty_block": "<PERSON><PERSON> Vazio", "create_new": "Criar nova", "native": "Nativa", "protected_cell_warning": "Não é possível alterar o valor de uma célula protegida"}, "TOAST": {"fail_id_length_exceeded": "Seu ID ultrapassa o limite de 64 caracteres", "fail_edit_workspace_color": "Falha ao editar cor do workspace", "duplicate_screen_error": "Falha ao duplicar tela", "error_project_load": "Erro ao carregar projetos disponíveis", "fail_rename_sheet": "Falha ao renomear planilha", "fail_delete_sheet": "Falha ao deletar planilha", "define_destination_emails": "Defina o email destino", "members_not_found": "Membro do projeto não encontrado", "fail_remove_member": "Falha ao remover membro.", "fail_to_rename_worksheet": "Falha ao renomear planilha", "fail_to_rename_screen": "Falha ao renomear tela", "file_not_found": "Arquivo não encontrado", "fail_to_upload_file": "Falha ao fazer upload do arquivo!", "select_module": "Selecione o módulo", "add_one_dimension_dataset": "Adicione pelo menos uma dimensão ao Dataset", "unable_add_same_dimension": "Não é possível adicionar a mesma dimensão", "email_code_was_not_confirmed": "O código de e-mail não foi confirmado", "failed_create_folder": "Falha ao criar nova pasta", "fail_to_rename_folder": "Falha ao renomear pasta", "fail_rename_workspace": "Falha ao renomear o workspace", "fail_delete_workspace": "Falha ao deletar o workspace", "user_already_registered": "O usuário já está cadastrado!", "fail_change_password": "<PERSON>alha ao trocar a senha.", "fail_change_name": "Falha ao trocar o nome.", "fail_change_profile_picture": "Falha ao trocar a foto de perfil.", "name_changed_successfully": "Nome alterado com sucesso.", "password_changed_successfully": "Senha alterada com sucesso.", "profile_photo_successfully_changed": "Foto de perfil foi alterada com sucesso.", "duplicated_name": "Nome duplicado.", "error_create_data_loader": "Falha ao criar importação", "error_create_online_table": "Falha ao criar tabela online", "cube_name_already_exists": "O nome do cubo já existe.", "folder_name_already_exists": "Falha ao adicionar pasta, o nome já existe.", "screen_name_already_used": "Nome de tela já usado nesta pasta.", "failed_load_screens": "Falha ao carregar telas!", "failed_load_content_dimension": "Falha ao carregar membros dessa dimensão!", "failed_invite_member": "Falha ao convidar novo membro", "merge_credentials_not_found": "Usuario sem credenciais para acessar este workspace!", "unable_delete_field": "Não foi possivel deletar o campo!", "failed_create_flap": "Falha ao criar aba!", "dimensions_belong_to_same_branch": "As dimensões pertencem a mesma branch.", "error_choosing_field": "Error ao escolher campo!", "error_running_query": "Falha ao executar a query", "error_assistant_id_invalid_key": "Chave incorreta, tente novamente!", "synchronize_table_error": "Falha ao sincronizar tabela", "error_connection_failed": "Falha na conexão", "error_jdbc_offline": "Selecione um JDBC que esteja online", "error_saving_data_loader": "Erro ao editar importação", "error_loading_data_loader_log": "Erro ao carregar log da importação", "error_assistant_id_no_model": "Você não possui créditos suficientes para ter acesso a GPT-4 API da OpenAI. Adicione créditos pelo painel de pagamentos da OpenAI e tenta novamente.", "merge": "Merge."}, "SCREENS": {"share_dashboard": "Compartilhar Dashboard"}}