{"SUPER_AI": {"try_again": "Try again", "free_credits": "After 5 credits, you can set up your own Google Gemini key, earn $300 in Google credit, and use it unlimited!", "an_last_execution_error": "In your last execution, the following executions failed. Please fix only them as all the others have already been executed:", "attention": "Attention!", "an_error_occurred": "An error occurred during execution,", "last_question_error": "For the last question, I received the following error. Please think step by step to resolve it.", "error_occurred": "An error occurred", "keep_calming": "Keep calm, sometimes the AI responds with something we didn't expect. Please try again.", "thoughts_time": "Thought for", "valid_objects": "Valid Objects", "building_in_real_time": "Building in Real Time", "no_data_available": "I don't have enough information to answer your question.", "deep_thinking": "Deep Thinking", "deep_thinking_descr": "It can take up to 2 minutes, but it has a higher accuracy.", "google_search": "Google Search", "google_search_grounding_descr": "Enable Google Search grounding for real-time information and web search capabilities", "code_execution": "Code Execution", "code_execution_descr": "Allows AI to generate and execute Python code for data analysis and document processing", "search_suggestions": "Search Suggestions", "web_search_results": "Web Search Results", "grounding_sources": "<PERSON><PERSON><PERSON>", "resource_exhausted": "Your Gemini API key must be tied to a Tier 1 project to work with the model we use, which is Gemini 2.5 Pro.", "clicking_here": "clicking here", "clicking_here_to_fix": "click here to fix", "internalServerError": "Internal Server Error", "errorOcurred": "An error occurred", "opsErrorOcurred": "Ops! An error occurred", "toContinue": "To continue, please try again", "tryAgainOnly": "Try again", "errorFetchingDetails": "An error occurred while fetching details", "errorExecutingTool": "An error occurred while executing the tool", "noValidPlanItems": "No valid plan items found", "noValidPlanItemsDetails": "The AI response does not contain valid plan items that can be executed. Try rephrasing your question or requesting a more specific action.", "payloadPlanParseError": "Error processing AI response data", "payloadPlanParseErrorDetails": "Could not process the AI response data. Please check if the response is in the correct format.", "jsonParsingError": "Response contains invalid keys", "jsonParsingErrorDetails": "Try asking the question again or create a new chat if the problem persists.", "apiKeyInvalidTitle": "Your Gemini key is out of resources", "apiKeyInvalidDetails": "Update the key and try again.", "errorRateLimit": "Rate limit exceeded. Please try again in a few moments.", "errorModelNotFound": "Please check if the model name is correct and try again.", "errorServiceUnavailable": "Service temporarily unavailable. Please try again in a moment.", "errorTimeout": "Timeout exceeded. Please try again or reduce the request size.", "errorInvalidArgument": "Invalid parameters for the model. Please check the model and limits.", "errorApiKey": "Problem with API key. Please check your settings.", "errorGeneric": "An unexpected error occurred. Please try again.", "errorNetworkError": "Connection failed. Please check your internet.", "errorPrematureStop": "We didn't get a response from Gemini", "errorPayloadTooLarge": "The uploaded file is too large. Please try a smaller file.", "hintRateLimit": "Wait a few moments or configure your own key.", "hintModelNotFound": "Please check if the model name is correct.", "hintServiceUnavailable": "Wait a few moments and try again.", "hintTimeout": "Reduce the request size or try again.", "hintInvalidArgument": "Please check the request parameters.", "hintApiKey": "Please check your API key settings.", "hintGeneric": "Please try again in a few moments.", "hintNetworkError": "Please check your internet connection.", "hintPrematureStop": "The connection was interrupted during processing.", "hintJsonParsingError": "The AI response contains invalid characters or structures that cannot be processed. Try asking the question again or create a new chat if the problem persists.", "of": "of", "streamInterruptedTitle": "We didn't get a response from Gemini", "streamInterruptedDetails": "The connection to <PERSON> was interrupted during streaming. Please try again.", "since": "Since", "executionPlan": "Execution Plan", "input": "Input", "output": "Output", "copiedSuccess": "Successfully copied", "clickToCopy": "Click to copy", "item": "<PERSON><PERSON>", "project_settings": "Project settings", "web_access": "Web Access:", "mobile_test": "Mobile Test:", "use_code": "- Use the code", "in_mitra_apps": "in Mitra Apps", "scan_qr_code": "- Scan the QR code below to download the app:", "ios": "iOS", "android": "Android", "courtesy_credits": "Bonus credits", "ai_credits": "AI Credits", "operations": {"tables": {"created": "Tables Created", "createFailed": "Failed to create Tables", "updated": "Tables Updated", "updateFailed": "Failed to update Tables", "removed": "Tables Removed", "removeFailed": "Failed to remove Tables"}, "attributes": {"added": "Attributes Added", "addFailed": "Failed to add Attributes", "updated": "Attributes Updated", "updateFailed": "Failed to update Attributes", "removed": "Attributes Removed", "removeFailed": "Failed to remove Attributes"}, "variables": {"added": "Variables Added", "addFailed": "Failed to add Variables", "removed": "Variables Removed", "removeFailed": "Failed to remove Variables"}, "forms": {"created": "Forms Created", "createFailed": "Failed to create Forms", "modified": "Forms Modified", "modifyFailed": "Failed to modify Forms", "removed": "Forms Removed", "removeFailed": "Failed to remove Forms"}, "screens": {"created": "Screens Created", "createFailed": "Failed to create Screens", "updated": "Screens Updated", "updateFailed": "Failed to update Screens", "removed": "Screens Removed", "removeFailed": "Failed to remove Screens"}, "dml": {"executed": "DML Executed", "executeFailed": "Failed to execute DML"}, "ddl": {"executed": "DDL Executed", "executeFailed": "Failed to execute DDL"}, "dbActions": {"added": "DB Actions Added", "addFailed": "Failed to add DB Actions", "removed": "DB Actions Removed", "removeFailed": "Failed to remove DB Actions"}, "actions": {"added": "Actions Added", "addFailed": "Failed to add Actions", "modified": "Actions Modified", "modifyFailed": "Failed to modify Actions"}, "component": {"removed": "Component Removed", "removeFailed": "Failed to remove Component"}, "tableComponent": {"added": "Table Added", "addFailed": "Failed to add Table", "modified": "Table Modified", "modifyFailed": "Failed to modify Table"}, "graphComponent": {"added": "Graph Added", "addFailed": "Failed to add Graph", "modified": "Graph Modified", "modifyFailed": "Failed to modify Graph"}, "crudListComponent": {"added": "CRUD List Added", "addFailed": "Failed to add CRUD List", "modified": "CRUD List Modified", "modifyFailed": "Failed to modify CRUD List"}, "listComponent": {"added": "List Added", "addFailed": "Failed to add List", "modified": "List Modified", "modifyFailed": "Failed to modify List"}, "labelComponent": {"added": "Label Added", "addFailed": "Failed to add Label", "modified": "Label Modified", "modifyFailed": "Failed to modify Label"}, "buttonComponent": {"added": "<PERSON><PERSON> Added", "addFailed": "Failed to add <PERSON><PERSON>", "modified": "Button Modified", "modifyFailed": "Failed to modify Button"}, "containerComponent": {"added": "Container Added", "addFailed": "Failed to add Container", "modified": "Container Modified", "modifyFailed": "Failed to modify Container"}, "imageComponent": {"added": "Image Added", "addFailed": "Failed to add Image", "modified": "Image Modified", "modifyFailed": "Failed to modify Image"}, "modal": {"added": "Modal Added", "addFailed": "Failed to add Modal", "modified": "Modal Modified", "modifyFailed": "Failed to modify Modal", "removed": "Modal Removed", "removeFailed": "Failed to remove <PERSON><PERSON>"}, "modalTab": {"removed": "Modal Tab Removed", "removeFailed": "Failed to remove Modal Tab"}, "selectorComponent": {"added": "Selector Added", "addFailed": "Failed to add Selector", "modified": "Selector Modified", "modifyFailed": "Failed to modify Selector"}, "kanbanComponent": {"added": "<PERSON><PERSON><PERSON> Added", "addFailed": "Failed to add Kanban", "modified": "Kanban Modified", "modifyFailed": "Failed to modify Kanban"}, "inputComponent": {"added": "Input Added", "addFailed": "Failed to add Input", "modified": "Input Modified", "modifyFailed": "Failed to modify Input"}, "htmlComponent": {"added": "HTML Added", "addFailed": "Failed to add HTML", "modified": "HTML Modified", "modifyFailed": "Failed to modify HTML"}, "defaultFailure": "Failed"}}, "DB_ATTRS": {"Number": "Use this attribute for numbers, except ZIP codes, CNPJ, CPF, or phone numbers. For these, use the Text type.", "Text": "Attribute for general text. Also use for ZIP codes, CNPJ, CPF, or phone numbers.", "Date": "Use this attribute for dates.", "SingleChoice": "Create a static list of options to select only one of them.", "LinkToDimension": "Connect this table to another through a related attribute.", "Formula": "Attribute that performs automatic calculations based on defined formulas."}, "S3": {"Planos e Preços": "Plans and Prices", "Planos e Preços - 3 colunas": "Plans and Prices - 3 columns", "Planos e Preços - 5 colunas": "Plans and Prices - 5 columns", "Fluxo de Processos": "Process flow", "Kanban com Cards Superiores": "<PERSON><PERSON><PERSON> with Superior Cards", "Fluxo de Processos 1": "Process flow 1", "Kanban com Insights": "<PERSON><PERSON><PERSON> with Insights", "Fluxo de Processos 2": "Process flow 2", "Kanban com 2 Cards de Insights": "<PERSON><PERSON><PERSON> with 2 Insights Cards", "Fluxo de Processos 3": "Process flow 3", "Análise de Dados": "Data analysis", "Análise de Dados 1": "Data analysis 1", "Dashboard com 4 Cards e 4 Quadrantes": "Dashboard with 4 Cards and 4 Quadrants", "Análise de Dados 2": "Data analysis 2", "Dashboard simples - 2 quadrantes": "Simple dashboard - 2 quadrants", "Análise de Dados 3": "Data analysis 3", "Dashboard 4 Quadrantes": "Dashboard 4 Quadrants", "Análise de Dados 4": "Data analysis 4", "Configurações": "Settings", "crm_short_description": "Create custom Customer Relationship Management (CRM) scenarios to boost your sales and customer loyalty.", "crm_long_description": "With this template, you can create custom CRM scenarios for your business, optimizing your sales strategies and improving your relationship with your customers. Choose from a variety of options and parameters, such as customer segmentation, marketing campaigns, lead tracking, and more. Combining these elements, you will be able to simulate different situations and evaluate the predicted results. Try different approaches and make informed decisions to improve the efficiency of your business operations and increase customer satisfaction.", "Comercial": "Commercial", "Gestão de Tarefas": "Task Management", "task_management_short_description": "Streamline your tasks and projects with this Task Management scenario template.", "task_management_long_description": "This template allows you to create custom Task Management scenarios for your team and your projects. With a variety of options such as setting priorities, assigning responsibilities, estimating time and tracking progress, you can simulate different workflows. Test different scenarios to optimize productivity, identify bottlenecks and improve meeting deadlines. With this model, your team will be better prepared to face challenges and work in a more efficient and coordinated way.", "Planejamento Orçamentário": "Budget planning", "budget_planning_short_description": "Make smarter financial decisions with this Budget Planning scenario template.", "budget_planning_long_description": "With this template, you can create Budget Planning scenarios for your company, helping you to make more informed financial decisions. Try different income and expense projections, simulate investments, cost cutting or expansions, and see how each scenario impacts the financial health of your business. By having a clear vision of possible future situations, you will be better prepared to face economic challenges and identify opportunities for sustainable growth. Use this template to plan your company's future with more confidence and security.", "Estratégico": "Strategic", "Financeiro": "Financial", "financial_short_description": "Discover valuable insights for the success of your business with this Business Intelligence (BI) scenario template.", "financial_long_description": "With this Business Intelligence (BI) scenario template, you can explore and analyze data to discover strategic information for the success of your business. Customize your scenarios by selecting relevant data sources, defining KPIs (Key Performance Indicators) and choosing metrics to analyze. Explore different variables and filters to test different hypotheses and discover valuable insights. By using this model, you will be able to make more informed decisions, identify trends, opportunities and challenges, and improve the efficiency of your operations. Leverage the full potential of your data to drive innovation and business growth with intelligence and strategy.", "CRM": "CRM", "BI": "BI", "RH": "RH", "Caixa de Entrada": "Inbox", "Em Andamento": "In Progress", "Concluído": "Completed", "Análises comerciais": "Business analysis", "Análises de folha de pagamento": "Payroll analytics", "Análises financeiras": "Financial analysis", "Análises contábeis": "Accounting analyzes", "Dia": "Day", "Produto": "Product", "Valor de vendas": "Sales value", "Margem de contribuição": "Contribution margin", "Cliente": "Client", "Vendedor": "<PERSON><PERSON>", "Quantidade de vendas": "Sales quantity", "natives": {"Data Criação": "Creation Date", "Usuário Criação": "Creation User", "Data Últ": {" Alteração": "Last Amendment Date"}, "Usuário Últ": {" Alteração": "User Last Changed"}, "Calendário": "Calendar", "Usuários": "Users", "Tipo de usuário": "User type", "Tela": "Screen", "Módulo": "<PERSON><PERSON><PERSON>", "Dia": "Day", "Mês": "Month", "Ano": "Year", "Trimestre": "Quarter", "Semana": "Week", "Dia da Semana": "Day of the Week", "Int - Dias no ano até hoje": "Int - Days in year to date", "Int - Ano Atual": "Int - Current Year", "Int - Mês Atual": "Int - Current Month", "Int - Dia Atual": "Int - Current Day", "Int -  Últ 12 meses": "Int - Last 12 months", "Int - Dias no mês até hoje": "Int - Days in month to date"}, "Listas Mobile (Mobile)": "Mobile Lists (Mobile)", "Lista Simples (Mobile)": "Simple List (Mobile)", "Lista Simples com Imagem (Mobile)": "Simple List with Image (Mobile)", "Lista Simples com Valor (Mobile)": "Simple List with Value (Mobile)", "Lista Duas Métricas (Mobile)": "Two Metrics List (Mobile)", "Lista Múltiplos Atributos (Mobile)": "Multiple Attributes List (Mobile)", "Lista Tasks (Mobile)": "Tasks List (Mobile)", "Listas": "Lists", "Lista Simples": "Simple List", "Lista Simples com Imagem": "Simple List with Image", "Lista Tasks": "Tasks List", "Cadastros": "Registrations", "Outros": "Others", "Kanban e Botão": "Kanban and Button", "Lista Simples e Botão": "Simple List and Button", "Quatro CRUDs + Botões": "Four CRUDs + Buttons", "Esquerda Fixa + 4 CRUDs": "Fixed Left + 4 CRUDs", "Uma Métrica | Sem Mapa": "One Metric | No Map", "Uma Métrica | Com Mapa": "A Metric | With Map", "Duas Métricas | Sem Mapa": "Two Metrics | No Map", "Duas Métricas | Com Mapa": "Two Metrics | With Map", "Planos e Preços 3 Faixas": "Plans and Prices 3 Bands", "Planos e Preços 5 Faixas": "Plans and Prices 5 Bands", "Formulário": "Form", "Tela Home": "Home Screen", "Todos": "All"}, "LOG_ERROR": {"no_code": "Error during execution", "100": "No connection to the database", "101": "Error in query", "102": "Duplicate table name", "103": "Duplicate cube name", "104": "Tables on the same branch", "105": "Base fell during execution", "106": "Table({id}) not found", "107": "Cube({id}) not found", "108": "Chip not found for main table", "109": "Mitra Connect queue timeout"}, "LANGFLOW": {"plan": {"ADD_TELAS": "Screen Creation", "ADD_DASHBOARD": "Screen Creation", "ADD_COMPONENT_BUTTON": "Button Creation", "ADD_COMPONENT": "Component Creation", "ADD_FORMS": "Form Creation", "ADD_ACTIONS": "Action Creation", "STREAM_OBJECT": "Stream Object", "STANDALONE": "Standalone Object", "UNKNOWN": "Unknown Object", "executed": "Executed plan", "TABELAS_DB": "Tables", "IMPORT": "Imports", "VARIAVEIS": "Variables", "TELAS": "Screens", "DASHBOARD": "Dashboards", "FORMS": "Forms", "COMPONENT": "Components", "DBACTIONS": "Database Actions", "DETAILS_MODAL": "Details modal", "GENERAL_LIST": "General List", "GENERAL_KANBAN": "General <PERSON><PERSON><PERSON>", "ACTIONS": "Actions", "component_screen": "Components created on the screen", "component_screen_short": "On screen", "CUBES_DB": "Datasets", "PROJECT_SETTINGS": "Project", "USE_FILES": "Files that I will upload", "action": {"screen_creation": "Creating screen", "button_creation": "Creating button", "component_creation": "Creating component", "form_creation": "Creating form", "action_creation": "Creating action", "object_creation": "Creating object", "unknown_object": "Processing object", "ADD": "add", "ALT": "update", "DELETE": "delete", "RUN_DML": "<PERSON><PERSON>ts that I will execute", "RUN_DDL": "<PERSON><PERSON>ts that I will execute", "ADD_COMPONENT": "Components that I will add", "ADD_CUBES_DB": "Datasets that I will add", "ADD_TELAS": "Screens that I will add", "ADD_DASHBOARD": "Dashboards that I will add", "ADD_FORMS": "Forms that I will add", "ADD_ACTIONS": "Actions that I will add", "ADD_DBACTIONS": "Database actions that I will add", "ADD_MODAL": "Details modal that I will add", "ADD_TABELAS_DB": "Tables that I will add", "ADD_VARIAVEIS": "Variables that I will add", "ADD_DETAILS_MODAL": "Details modals that I will add", "ADD_COMPONENT_GENERAL_LIST": "General list that I will add", "ADD_COMPONENT_GENERAL_KANBAN": "General ka<PERSON><PERSON> that I will add", "ADD_COMPONENT_CARD_GROUP": "Card group that I will add", "ADD_COMPONENT_CARDS_GALLERY": "Cards gallery that I will add", "ADD_COMPONENT_BARCHART_NEW": "Bar chart that I will add", "ADD_COMPONENT_PIECHART_NEW": "Pie chart that I will add", "ADD_COMPONENT_MENU": "Menu that I will add", "ADD_COMPONENT_PRETTY_LIST": "List that I will add", "ADD_COMPONENT_DYNAMIC_FORM": "Dynamic form that I will add", "ADD_COMPONENT_BORDA": "Border that I will add", "ADD_COMPONENT_AREACHART_NEW": "Area chart that I will add", "ADD_COMPONENT_MENU_MOBILE": "Mobile menu that I will add", "ADD_COMPONENT_CHECKOUT_CARD": "Checkout card that I will add", "ADD_COMPONENT_GANTT": "Gantt chart that I will add", "ADD_COMPONENT_CALENDAR": "Calendar that I will add", "ADD_COMPONENT_CHECKLIST": "Checklist that I will add", "ADD_COMPONENT_PROGRESSBAR_NEW": "Progress bar that I will add", "ADD_COMPONENT_PROGRESSBAR_LIST": "Progress bar list that I will add", "ADD_COMPONENT_CARD_SELECTOR": "Card selector that I will add", "ADD_COMPONENT_RICH_TEXT": "Rich text that I will add", "ADD_COMPONENT_RESUME_CARD": "Resume card that I will add", "ADD_COMPONENT_ATTACHMENT": "Attachment that I will add", "ADD_COMPONENT_TABS_CONTAINER": "Tabs container that I will add", "ADD_PROJECT_SETTINGS": "Project settings that I will add", "DELETE_TELAS": "Screens that I will delete", "DELETE_DASHBOARD": "Dashboards that I will delete", "DELETE_CUBES_DB": "Datasets that I will delete", "DELETE_COMPONENT": "Components that I will delete", "DELETE_FORMS": "Forms that I will delete", "DELETE_ACTIONS": "Actions that I will delete", "DELETE_DBACTIONS": "Database actions that I will delete", "DELETE_MODAL": "Details modal that I will delete", "DELETE_TABELAS_DB": "Tables that I will delete", "DELETE_VARIAVEIS": "Variables that I will delete", "ALT_TABELAS_DB": "Fields I want to create within a table", "UPLOAD_PUBLIC_FILE": "Files that I will upload", "UPLOAD_LOADABLE_FILE": "Files that I will upload", "ALT_DETAILS_MODAL": "Details modals that I will update", "ALT_CUBES_DB": "Datasets that I will update", "ALT_COMPONENT": "Components that I will update", "ALT_TELAS": "Screens that I will update", "ALT_DASHBOARD": "Dashboards that I will update", "ALT_FORMS": "Forms that I will update", "ALT_ACTIONS": "Actions that I will update", "ALT_DBACTIONS": "Database actions that I will update", "ADD_IMPORT": "Imports that I will add", "USE_FILES_AS_PUBLIC": "Files that I will upload (public)", "USE_FILES_AS_LOADABLE": "Files that I will upload (import)", "ALT_MODAL": "Details modal that I will update", "ALT_VARIAVEIS": "Variables that I will update", "ALT_COMPONENT_GENERAL_LIST": "General list that I will update", "ALT_COMPONENT_GENERAL_KANBAN": "General ka<PERSON><PERSON> that I will update", "ALT_COMPONENT_CARD_GROUP": "Card group that I will update", "ALT_COMPONENT_CARDS_GALLERY": "Cards gallery that I will update", "ALT_COMPONENT_BARCHART_NEW": "Bar chart that I will update", "ALT_COMPONENT_PIECHART_NEW": "Pie chart that I will update", "ALT_COMPONENT_MENU": "<PERSON>u that I will update", "ALT_COMPONENT_PRETTY_LIST": "List that I will update", "ALT_COMPONENT_DYNAMIC_FORM": "Dynamic form that I will update", "ALT_COMPONENT_BORDA": "Border that I will update", "ALT_COMPONENT_AREACHART_NEW": "Area chart that I will update", "ALT_COMPONENT_MENU_MOBILE": "Mobile menu that I will update", "ALT_COMPONENT_CHECKOUT_CARD": "Checkout card that I will update", "ALT_COMPONENT_GANTT": "Gantt chart that I will update", "ALT_COMPONENT_CALENDAR": "Calendar that I will update", "ALT_COMPONENT_CHECKLIST": "Checklist that I will update", "ALT_COMPONENT_PROGRESSBAR_NEW": "Progress bar that I will update", "ALT_COMPONENT_PROGRESSBAR_LIST": "Progress bar list that I will update", "ALT_COMPONENT_CARD_SELECTOR": "Card selector that I will update", "ALT_COMPONENT_RICH_TEXT": "Rich text that I will update", "ALT_COMPONENT_RESUME_CARD": "Resume card that I will update", "ALT_COMPONENT_ATTACHMENT": "Attachment that I will update", "ALT_COMPONENT_TABS_CONTAINER": "Tabs container that I will update", "ALT_PROJECT_SETTINGS": "Project settings that I will update", "alt": {"ADD_COMPONENT_GENERAL_LIST": "General list created", "ADD_COMPONENT_GENERAL_KANBAN": "General <PERSON><PERSON><PERSON> created", "ADD_COMPONENT_CARD_GROUP": "Card group created", "ADD_COMPONENT_CARDS_GALLERY": "Cards gallery created", "ADD_COMPONENT_BARCHART_NEW": "Bar chart created", "ADD_COMPONENT_PIECHART_NEW": "Pie chart created", "ADD_COMPONENT_MENU": "<PERSON><PERSON> created", "ADD_COMPONENT_PRETTY_LIST": "List created", "ADD_COMPONENT_DYNAMIC_FORM": "Dynamic form created", "ADD_COMPONENT_BORDA": "Border created", "ADD_COMPONENT_AREACHART_NEW": "Area chart created", "ADD_COMPONENT_MENU_MOBILE": "Mobile menu created", "ADD_COMPONENT_CHECKOUT_CARD": "Checkout card created", "ADD_COMPONENT_GANTT": "Gantt chart created", "ADD_COMPONENT_CALENDAR": "Calendar created", "ADD_COMPONENT_CHECKLIST": "Checklist created", "ADD_COMPONENT_PROGRESSBAR_NEW": "Progress bar created", "ADD_COMPONENT_PROGRESSBAR_LIST": "Progress bar list created", "ADD_COMPONENT_CARD_SELECTOR": "Card selector created", "ADD_COMPONENT_RICH_TEXT": "Rich text created", "ADD_COMPONENT_RESUME_CARD": "Resume card created", "ADD_ATTACHMENT": "Attachment created", "ADD_COMPONENT_TABS_CONTAINER": "Tabs container created", "ADD_PROJECT_SETTINGS": "Project settings created", "ALT_COMPONENT_GENERAL_LIST": "General list updated", "ALT_COMPONENT_GENERAL_KANBAN": "General kanban updated", "ALT_COMPONENT_CARD_GROUP": "Card group updated", "ALT_COMPONENT_CARDS_GALLERY": "Cards gallery updated", "ALT_COMPONENT_BARCHART_NEW": "Bar chart updated", "ALT_COMPONENT_PIECHART_NEW": "Pie chart updated", "ALT_COMPONENT_MENU": "Menu updated", "ALT_COMPONENT_PRETTY_LIST": "List updated", "ALT_COMPONENT_DYNAMIC_FORM": "Dynamic form updated", "ALT_COMPONENT_BORDA": "Border updated", "ALT_COMPONENT_AREACHART_NEW": "Area chart updated", "ALT_COMPONENT_MENU_MOBILE": "Mobile menu updated", "ALT_COMPONENT_CHECKOUT_CARD": "Checkout card updated", "ALT_COMPONENT_GANTT": "Gantt chart updated", "ALT_COMPONENT_CALENDAR": "Calendar updated", "ALT_COMPONENT_CHECKLIST": "Checklist updated", "ALT_COMPONENT_PROGRESSBAR_NEW": "Progress bar updated", "ALT_COMPONENT_PROGRESSBAR_LIST": "Progress bar list updated", "ALT_COMPONENT_CARD_SELECTOR": "Card selector updated", "ALT_COMPONENT_RICH_TEXT": "Rich text updated", "ALT_COMPONENT_RESUME_CARD": "Resume card updated", "ALT_ATTACHMENT": "Attachment updated", "ALT_COMPONENT_TABS_CONTAINER": "Tabs container updated", "ALT_PROJECT_SETTINGS": "Project settings updated", "DELETE_COMPONENT": "Deleted components", "DELETE_TELAS": "Deleted screens", "DELETE_DASHBOARD": "Deleted dashboards", "DELETE_FORMS": "Deleted forms", "DELETE_ACTIONS": "Deleted actions", "DELETE_DBACTIONS": "Deleted database actions", "DELETE_MODAL": "Deleted details modal", "DELETE_TABELAS_DB": "Deleted tables", "DELETE_VARIAVEIS": "Deleted variables", "DELETE_CUBES_DB": "Deleted datasets", "ALT_COMPONENT": "Components updated", "ALT_TELAS": "Screens updated", "ALT_DASHBOARD": "Dashboards updated", "ALT_ACTIONS": "Actions updated", "ALT_DBACTIONS": "Database actions updated", "ALT_MODAL": "Details modal updated", "ALT_TABELAS_DB": "Tables updated", "ALT_VARIAVEIS": "Variables updated", "ALT_CUBES_DB": "Datasets updated", "ADD_COMPONENT": "Components created", "ADD_TELAS": "Screens created", "ADD_DASHBOARD": "Dashboards created", "ADD_FORMS": "Forms created", "ADD_ACTIONS": "Actions created", "ADD_DBACTIONS": "Database actions created", "ADD_MODAL": "Details modal created", "ADD_TABELAS_DB": "Tables created", "ADD_VARIAVEIS": "Variables created", "RUN_DML": "Executed scripts", "ADD_DETAILS_MODAL": "Details modals created", "ADD_CUBES_DB": "Datasets created", "ADD_IMPORT": "Imports created", "USE_FILES_AS_PUBLIC": "Files that I will upload (public)", "USE_FILES_AS_LOADABLE": "Files that I will upload (import)", "ALT_FORMS": "Forms updated", "ADD_ATRIBUTES_DB": "Fields created within a table", "DELETE_ATRIBUTES_DB": "Fields deleted within a table", "ALT_ATRIBUTES_DB": "Fields updated within a table", "ALT_DETAILS_MODAL": "Details modals updated", "RUN_DDL": "Executed scripts"}}, "DML": "DML Operation", "DDL": "DDL Operation"}}, "SANKHYA": {"request_license": "Request license"}, "GREETINGS": {"welcome": "welcome"}, "BUTTONS": {"cancel": "Cancel", "save": "Save", "add": "Add", "create": "Create", "next": "Next", "start": "Start", "complete": "Complete", "update": "Update", "delete": "Delete", "back": "Back", "replace": "Replace", "remove": "Remove", "access": "Access", "set_up": "Set up", "confirm": "Confirm"}, "TIPS": {"request_access": "Please contact the person in charge to request.", "NO_SCREEN": {"title": "Home Screen Not Configured", "subtitle": "Contact the person in charge to set up the home screen and start using the system"}, "NO_ACCESS": {"no_has_access": "Oops, you don't have access", "maintenance_project": "Project under maintenance", "try_latter": "Try to access it again soon."}}, "NO_SPACES": {"title": "Oops! You are not in any workspace yet.", "email_access": "You accessed with the email", "not_right": "It's not right?", "use_another_email": "Log in with another email", "already_client": "I'm already a Mitra client", "request_access": "Please contact the workspace administrator to request access.", "beta_tester": "I'm a beta tester of the new version", "suport_to_register": "If you have already been selected as a beta tester, please contact us and we will adjust your table.", "try_new_version": "I want to try the new version of <PERSON><PERSON>", "wait_list_tip": "Join the waiting list to receive early access!", "wait_list_enter": "Join the waiting list", "email_added": "Email added"}, "LOGGED": {"manage_account": "Manage account", "current_workspace": "Current workspace", "change_workspace": "Change workspace", "new_workspace": "New workspace", "portuguese": "Portuguese", "portuguese_brazil": "Brazilian Portuguese", "english": "English"}, "FORMS": {"new_detail_modal": "New record details", "new_form": "New form", "required": "Required field", "fill_required_fields": "Fill in the required fields!", "duplicate_name": "Duplicate Name", "select_field_below": "Select a field below", "fill_form": "Fill out the following form with your information. Our sales team will contact you soon.", "talk_to_us": "Talk to us"}, "ACCOUNT": {"personal_information": "Personal information", "update_name_and_photo_here": "Update your name and photos here.", "your_photo": "Your photo", "photo_appears_profile": "The photo that appears on your profile.", "password_update": "First enter your current password to update.", "current_password": "Current password", "enter_current_password": "Insert your current password", "new_password": "New password", "enter_new_password": "Insert your new password", "confirm_new_password": "Confirm new password", "re_enter_new_password": "Re-enter your new password", "language": "Language", "language_config_body": "Select the application language. Worksheet formulas also change by language.", "logout": "Logout", "field_cannot_empty": "This field cannot be empty.", "click_to_select": "Click to select", "or_drag_and_drop_image": "or drag and drop the image here."}, "LOGIN": {"user_not_found": "User not found", "invalid_email_password": "Invalid email or password", "error": "<PERSON><PERSON> failed", "login_title": "Sign in", "login_subTitle": "Please log in to continue.", "insert_email": "Enter your email", "insert_password": "Enter your password", "forgot_password": "I forgot the password", "please_insert_email": "Please enter your email!", "please_insert_password": "Please enter your password!", "please_insert_name": "Please enter your name!", "account_login": "Login with", "create_account": "Create account", "not_have_account": "Don't have an account yet?", "create_account_here": "Create yours here.", "create_mitra_account": "Create your {name} account", "sign_up": "Sign up", "sign_up_msg": "Fill in the information below to get started.", "enter_name": "Enter your name", "invalid_email": "Invalid email", "at_least_6_characters": "At least 6 characters", "password_must_contain": "Your password must contain:", "at_least_6_characters_2": "At least 6 characters", "uppercase_and_lowercase": "Uppercase and lowercase letters", "numbers": "Numbers", "already_have_account": "Already have an account?", "login": "<PERSON><PERSON>", "check_email": "Check your email", "sended_code_to": "We have sent a verification code to", "didnt_receive_code": "Didn't receive the code?", "wait": "Wait", "resend": "to resend", "click_resend": "Click to resend", "back_registration_screen": "Back to the sign-up screen", "back_login_screen": "Back to the login screen", "verification_completed": "Verification completed!", "redirecting_your_first": "We are redirecting you to your first Workspace!", "forgot_password_2": "Forgot password?", "verify": "Verify", "reset_password": "Reset your password", "reset": "Reset", "input_password_again": "Enter your password again", "please_confirm_password": "Please confirm your password!", "password_reset_description": "Your password must be different from previously used passwords."}, "PROJECT": {"remove_header_warning_html": "By removing the default platform header, the project will be displayed without a {bold1}navigation menu{bold2} or {bold1}logout button{bold2}.\nThis option is only recommended for developers who will set up their own custom menu.\n\nYou can revert this option at any time in the project settings, under the Web tab.", "remove_native_header": "Remove native header", "remove_header": "Remove header", "json_config": "JSON Configuration", "preparing_project": "Preparing your project...", "first_project": "My first project", "new_html_screen": "New HTML Screen", "html_screen": "HTML Screen", "delete_dashboard": "Delete Dashboard", "not_verified": "Not verified", "verified": "Verified", "request_verification": "Request verification", "send_email_action": "E-mail for action ”Send Email”", "recover_password_email": "Password recovery e-mail", "create_account_email": "Create account e-mail", "invite_email_body": "You can use HTML to configure the text. \nMandatory Variables: $codigo (Returns the verification code to recover the password) \nAvailable Variables: $emailUsuario (Returns the email of the invited user)", "invite_email_body_hint": "You can use HTML to configure the text. \nMandatory Variables: $link (Returns the project invitation link) \nAvailable Variables: $emailUsuario (Returns the email of the invited user) - $projeto (Returns the Project name)", "only_verified_emails": "Only verified e-mails can be used.", "email_body": "E-mail body", "title_email": "E-mail Title", "sender": "Sender", "invite_email": "Invitation E-mail", "register_email": "Register new E-mail", "email_registration": "E-mail Registration", "email_registration_preview": "Preview", "email_registration_edit": "Edit", "project_type": "Project type", "home_screen": "Solution Home Screen *", "configure_project": "Configure project", "delete_sure": "Are you sure? All data will be deleted...", "cancel_subscription": "Cancel subscription", "native_screens": "Native screens", "publish_screen": "Publish screen", "unpublish_screen": "Unpublish tela", "no_published_screens": "No published screens", "import_spreadsheet": "Import Spreadsheet", "project_data": "Project data", "project_name": "Project name", "insert_project_name": "Insert project name", "this_field_is_mandatory": "This field is mandatory.", "empty_project_text_1": "You don't have any worksheets or folders yet,", "empty_project_text_2": "start creating!", "import_file": "Import spreadsheet", "click_to_select": "Click to select", "or_drag_and_drop": "or drag and drop the file here.", "quick_access": "Quick access", "create_screen": "Create Screen", "create_dashboard": "Create Dashboard", "create_new_blank_screen": "Create new blank screen", "create_mobile_screen": "Create Mobile Screen", "create_document": "Create Document", "create_new_blank_mobile_screen": "Create new blank mobile screen", "create_new_blank_dashboard": "Create new empty dashboard", "published": "Published", "unpublished": "Unpublished", "show_unpublished": "Show unpublished", "show_unpublished_screens": "Show unpublished screens", "hide_unpublished_screens": "Hide unpublished screens", "new_screen": "New screen", "new_document": "New document", "duplicate_screen": "Duplicate screen", "duplicate_doc": "Duplicate document", "where_create_screen": "Where would you like to create the new screen?", "where_create_dashboard": "Where would you like to create the new dashboard?", "new_mobile_screen": "New mobile screen", "duplicate_mobile_screen": "Duplicate mobile screen", "where_create_mobile_screen": "Where would you like to create the new mobile screen?", "screen": "Screen", "mobile_screen": "Mobile screen", "error_deleting_folder": "Error deleting folder", "error_deleting_screen": "Error deleting screen", "spreadsheets": "Spreadsheets", "new_folder": "Folder", "insert_name": "Insert name", "create_folder": "Create Folder", "create_new_project": "Create new project", "create_new_project_dev": "Create New Project", "create_new_folder": "Create a new folder", "delete_folder": "Delete folder?", "delete_folder_body_1": "Are you sure you want to delete the folder", "delete_folder_body_2": "This action cannot be undone and all data referring to the folder will be deleted!", "delete_screen": "Delete screen?", "delete_doc": "Delete document?", "delete_screen_body_1": "Are you sure you want to delete the screen", "delete_dashboard_body_1": "Are you sure you want to delete the dashboard", "delete_doc_body_1": "Are you sure you want to delete the document", "delete_screen_body_2": "This action cannot be undone and all data referring to the screen will be deleted!", "delete_doc_body_2": "This action cannot be undone and all data referring to the document will be deleted!", "do_not_have_modules_yet": "You don't have any modules yet, start creating!", "do_not_have_screen_yet": "You don't have any screen yet, start creating!", "no_access": "You do not have access to this workspace", "no_project_selected": "No project selected", "new_dashboard": "New Dashboard", "choose_model": "Choose a model", "choose_template": "Choose a template", "white_screen": "White screen", "white_list": "Blank list", "create_screen_from_scratch": "Create a screen from scratch", "enable_freedb": "Use analytics database", "TEMPLATE": {"use_demo_template": "Use demo", "tweet_template_description": "Includes title, tweet text, support for images/videos, empty state, and interactions like hover and selected.", "checkbox_template_description": "Includes title, support text, and standard, selected, and hover states.", "select_template": "Select a template", "template_gallery": "Template Gallery", "use_template": "Use Template", "start_from_scratch": "Start from scratch", "search_icons": "Search icons", "create_scratch": "Create from scratch", "project_type": "Project type", "other_type": "Other type", "other_type_name": "Other type name", "process_flow": "Process flow", "other": "Other", "start_pipe": "Let's start by customizing your pipe!", "pipe_steps": "Pipe phase", "pipe_steps_text": "You can edit existing ones, delete or add new ones.", "card_area": "Card Fields", "card_area_text": "You can edit existing ones, delete or add new ones.", "new_step": "New phase"}, "duplicate_project": "Duplicate project", "copy_of": "Copy of", "must_be_creator_in_workspace": "You must be a creator in a Workspace to perform this action.", "send_to_qa": "Send to QA", "successfully_duplicated_qa_base": "Successfully duplicated in QA base", "load_users": "Copy users", "error": "error | errors", "to_correct": "to correct", "search_for_errors": "Search for errors", "no_errors_found": "No errors found.", "last_run": "Last run", "to_update": "Update", "looking_for_errors": "Looking for errors...", "in_few_moments_your_action_will_completed": "In a few moments your action will be completed", "existing_stores": "Select template", "folder_name": "Create new template", "insert_base_name": "Insert base name", "there_already_base_with_that_name": "There is already a base with that name", "copie_data_to_json_model": "Copy the data below into the json template:", "template_creation": "Template creation: ", "template_update": "Template update: ", "sure_want_to_update": "Are you sure you want to update?", "sure_want_to_create": "Are you sure you want to create?", "enable_mobile_version_for_project": "Enable mobile version for this project", "project_appears_on_mobile": "The project appears in the project list within the cell phone", "has_home_screen": "Has home screen", "if_dont_you_can_access_any_published_screen": "If you don't have one, you can access any of the published screens", "solution_home_screen": "Choose your home screen", "choose_home_screen": "Choose your home screen", "screen_opens_by_default_when_user_enters_the_app": "This is the screen that opens by default when the user enters the application.", "screen_opens_by_default_when_user_enters_the_web": "This is the screen that opens by default when the user enters the application", "manage_and_delete_api_keys": "Manage and delete API keys", "integrate_mitra_with_any_other_system_api": "Integrate ${system} with any other system API", "key_name": "Key name", "private_key": "Private key", "create_new_api_key": "Create new API key", "you_do_not_have_any_api_key": "You do not have any API keys created yet.", "choose_tables_will_be_released_for_api": "Choose the tables that will be released for connection via the API.", "selected_tables": "Selected Items", "copy_url": "Copy URL", "copy_example": "Copy Example", "delete_key": "Delete key", "create_api_key": "Create API key", "enter_name_for_your_api_key": "Enter a name for your API key", "basic_information": "Basic Information", "release_for_end_user": "End User Releases", "home_screen_solution": "Solution Home Screen*", "home_screen_information": "This is the screen that opens by default when the user enters the application.", "dynamic_home_screen": "Dynamic home screen by profile", "dynamic_home_screen_information": "Allows you to define a home screen for each existing profile.", "no_profile_registered": "No profile registered", "profile": "Profile", "home_screen_header": "Home Screen", "duplicate_dashboard": "Duplicate Dashboard", "playground_info": "Allows users to access the Playground (Self Service BI)", "allow_dashboard_users": "Allow dashboard creation for users", "allows_users_create_screens": "Allows users to create dashboards and screens from Lila commands", "lila_home_page": "Lila as home page", "shared_with_me": "Shared with me", "lila_info": "Allows your end users to use copilot within preview mode", "ai_info": "Allows users to access {name} (our AI)", "lila_home_page_info": "Set <PERSON> as your project's home page.", "allow_lila_to_execute_actions": "Allow <PERSON> to perform actions on your project", "profile_tools_info": "You must fill out your users' profiles and define which tools (actions and tables) will be made available to each profile.", "view_tools_schema": "View tools schema", "teach_ai_to_work_with_tools": "Teach AI to work with tools using natural language.", "guidelines_for_ai": "Guidelines for AI", "write_guidelines": "Write the guidelines", "choose_connections": "Choose which connections will be available", "allow_to_import_csv_sql": "Allows users to import data via CSV or SQL.", "screen_plans_and_prices": "Plans and pricing screen", "screen_plans_and_prices_info": "Choose plans and pricing screen", "screen_show_attribute": "This screen will appear if the blocking attribute is active.", "lock_attribute": "Lock attribute", "choose_blocking_attribute": "Choose the blocking attribute", "choose_blocking_attribute_info": "Shows the lock screen according to the value defined when registering.", "delete_project_warning": "Deleting your project is an irreversible action that will result in the complete deletion of your projects and data, with no possibility of recovery.", "create_dashboard_with": "Create Dashboard with {name}", "screens": "Screens", "registrations": "Registrations", "locks": "Locks", "privacy": "Project Privacy", "privacy_hint": "Define who can access your application: {p1} allows free access via link; {p2} requires authentication; {p3} restricts access to authorized and logged-in users only.", "public": "Public without login", "public_with_login": "Public with login", "private": "Private", "chose_lock_screen": "Choose the lock screen", "lock_screen_hint": "This screen will appear according to the lock attribute.", "organize_screens_by_sessions": "Organize screens by sections in the menu", "show_menu": "Show Menu", "show_menu_tip": "Native system menu, where published screens appear.", "this_option_divides_the_preview_mode_navigation": "This option divides the end user navigation menu into sections, based on the project folder structure.", "project_frozen": "Frozen project", "project_frozen_info": "This project is frozen due to inactivity in the last 30 days.", "click_here_to_restore": "Click here to restore your project.", "general": "General", "advanced": "Advanced", "integrations": "Integrations", "mobile_publishing": "Mobile Publishing", "mitra_apps_header": "Mitra Apps - Testing Area", "mitra_apps_info": "Mitra Apps is Mitra's mobile platform where you can test your app with the community. When you publish your project, a unique PIN is generated, granting access to the Mitra Apps testing area. With the PIN, you can test your app directly in the mobile app, ensuring that it is working as expected before submitting it to the public catalog.", "publish_mitra_apps": "Publishing on Apple Store and Play Store", "publish_mitra_apps_info": "To make your app available on Android and iOS platforms, visit our GitHub repository and follow the documentation.", "github_repository": "GitHub Repository", "publication_key": "Publication key", "lila_for_final_users": "{name} for end users", "additional_considerations": "Additional Considerations", "add_considerations": "Add considerations", "cosideration_details_1": "Use this field to add information and considerations that will be concatenated with end-user prompts. These guidelines can help", "cosideration_details_2": "to provide more specific and relevant answers based on the instructions provided.", "data_for_ai": "Data for AI", "lila_for_end_users": "{name} for end users", "change_image": "Change image", "login_using_sso": "Login using SSO", "allow_login_with_google_or_microsoft": "Allow your users to login with Google or Microsoft", "create_account": "Create account", "allow_users_create_accounts": "Allow users to create their accounts without invitation", "total_users": "Total users", "developers_users": "Developers users", "business_users": "Business users", "lila_credits": "<PERSON>'s Credits (AI)", "credit_balance": "Credit balance", "add_credits": "Add credits", "recharge_history": "Recharge history", "transaction_history": "Transaction history", "confirm_payment": "Confirm payment", "choose_value": "Choose value", "other_value": "Other value", "enter_cpf_cnpj": "Enter CPF or CNPJ", "card_details": "Card details", "project": "Project", "project_not_have_prepared_data_yet": "Your project does not have prepared data yet", "prepare_data": "Prepare data", "select_tables": "Select tables", "select_table_that_you_want": "Select the tables that you want to", "will_have_access": "will have access:", "publish_your_project": "Publish your project", "publish_project_body": "Publish your project in seconds. Simple, fast and you can unpublish it whenever you need.", "mysite": "mysite", "code_mitra_apps_development_mode": "Use this code in Mitra Apps to access development mode, where you can test your mobile app before publishing it.", "standard_mode": "Standard mode", "set_default_mode_for_lila": "Sets the default mode for <PERSON> when starting a new chat", "analyze_components_from_other_screens": "Analyze components from other screens", "defines_whether_components_from_other_screens": "Defines whether components from other screens will be analyzed in context by default", "lila_ai": "<PERSON> (AI)", "content_copied_clipboard": "Content copied to clipboard", "error_copying_content": "Error copying content"}, "SIGNUP": {"error": "Fail to signup", "passwords_not_the_same": "Passwords are not the same."}, "AUTH": {"invalid_code": "Invalid code!", "code_validation_error": "Fail to validate code", "reset_password_error": "Fail to reset password"}, "dates": {"today": "Today", "yesterday": "Yesterday", "last_7_days": "Last 7 days", "last_30_days": "Últimos 30 dias"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "GLOBAL": {"no_icon_found": "No icons found for", "do_upgrade": "Upgrade", "loading": "Carregando...", "expand": "Expand", "COMPONENT": {"input_attach": "Attachment input", "input_button": "Button input", "react": "React", "table": "Table", "graph": "Graph", "crud_list": "CRUD List", "label": "Label", "button": "<PERSON><PERSON>", "container": "Container", "imagem": "Image", "image": "Image", "kanban": "Ka<PERSON><PERSON>", "react_list": "React List", "input": "Input", "html": "HTML", "template": "Template", "general": "General", "general_list": "General List", "selector": "Selector", "list": "List", "generic": "Component", "cardboard": "Cardboard", "input_text": "Input", "input_dropdown": "Dropdown input", "barchart": "Barchart", "piechart": "Piechart", "input_number": "Number input", "input_date": "Date input", "input_textarea": "Textarea input", "menu": "<PERSON><PERSON>", "card_group": "Cards", "barchart_new": "Barchart", "piechart_new": "Piechart", "general_list_qa": "Table"}, "how_works": "How it works", "grammar": {"a": "a", "o": "the", "the": "the", "and": "and", "to": "to", "with": "with", "for": "for", "on": "on", "by": "by", "from": "from", "of_the": "of the", "of_a": "of a", "of_an": "of an", "of_the_a": "of a", "of_the_an": "of an", "of_the_the": "of the", "of_the_the_a": "of the", "of_the_the_an": "of the", "of_the_a_the": "of a", "of_the_a_the_a": "of a", "of_the_a_the_an": "of a"}, "credits": "Credits", "connect": "Connect", "unknown": "Unknown", "that_i_will": "that I will", "setup_key": "Configure key", "setup_key_of": "Setup AI Key", "name_of_workspace": "Workspace name", "video": "Video", "template": "Template", "action": "Action", "detail": "Record Details", "open_preview": "Open preview mode", "open_app": "Open published project", "playground_access": "Playground access", "init": "Home", "type": "Type", "soon": "Soon", "community": "Community", "phone": "Phone", "developer": "Developer", "corporate": "Corporate", "first_vowel": "The", "image": "Image", "general": "General", "or": "or", "month": "month", "includes": "includes", "select_all": "Check all", "edit_appearance": "Edit appearance", "see_logs": "See logs", "copy_link": "Copy link", "copied_link": "Copied link", "copied": "<PERSON>pied", "click_to_copy": "Click to copy", "import": "Import", "origin": "Origin", "name": "Name", "options": "Options", "delete_alert": "Are you sure you want to delete?", "404_message": "Page not found", "actions": {"continue": "Continue", "back": "Back", "apply": "Apply", "delete": "Delete", "filter": "Filter", "create": "Create", "send": "Send", "reset": "Reset", "update": "Update", "edit": "Edit", "remove": "Remove"}, "ERRORS": {"general": "Oops! Something went wrong, reload the page", "try_again": "Oops! Something went wrong, please try again", "rename_error": "Fail to rename {name}", "load_error": "Fail to load {name}", "ops_some_error": "Oops! Something went wrong", "reload_page": "Try reloading the page", "not_found": "Oops, page not found!", "verify_address": "Please check if the address is correct."}, "search2": "Search", "look_for": "Search", "form": "Form", "search": "Search", "confirm": "Confirm", "new_project": "New project", "new_project_without_prompt": "New project without prompt", "projects": "Projects", "search_projects": "Search projects...", "search_workspace": "Search workspace...", "open": "Open", "rename": "<PERSON><PERSON>", "duplicate": "Duplicate", "move": "To move", "spreadsheet": "Spreadsheet", "folder": "Folder", "edit": "Edit", "share": "Share", "check_all": "Check all", "link_tags": "Link tags", "linked_tags": "Linked tags", "knowledge_base": "Knowledge base", "make_knowledge_base": "Make knowledge base", "make_knowledge_base_message": "By promoting this project as the knowledge base, the current one will no longer be the main base and the linked projects will lose their connections with it.", "remove_knowledge_base": "Remove knowledge base", "remove_knowledge_base_message": "By removing this project as the knowledge base, all currently linked projects will lose access to their tags and associations. This action is irreversible.", "remove_knowledge_base_title": "Remove knowledge base", "make_public": "Make public", "make_private": "Make private", "public_chat": "Public chat", "delete": "Delete", "new": "New", "email": "Email", "password": "Password", "permission": "Permission", "invite": "Invite", "log_out": "Log out", "start": "Start", "insert": "Insert", "close": "Close", "settings": "Settings", "data": "Data", "correct": "Correct", "all": "All", "select": "Select", "native": "Native", "cube": "C<PERSON>", "dimension": "Table", "table": "Table", "shortly": "Shortly", "no_results_found": "No results found.", "create_new": "Create new | Create new", "make_store": "Send to store", "search_2": "Search", "copy": "Copy", "required": "Required", "default": "<PERSON><PERSON><PERSON>", "example": "Example", "attributes": "Attributes", "arguments": "Arguments", "of": "of", "previous": "Previous", "next": "Next", "select_files": "Select files", "restore": "Rest<PERSON>", "history": "History", "current_version": "Current Version", "finish": "Finish"}, "CREATE_COLUMN_MODAL": {"text": "Use for long or indefinitely long texts, such as descriptions, comments, or extensive content.", "varchar": "Ideal for short texts with a defined length. The default limit is 256 characters, but this can be adjusted.", "extra_options": "Extra options", "field_size": "Field size", "edit_column_title": "Edit column", "create_new_column_title": "Create new column", "labels": {"name": "Name", "type": "Type", "default_value": "Default value"}, "placeholders": {"column_name": "Enter column name", "select": "Select", "null": "NULL"}, "foreign_key_section_title": "Connection with another table (Foreign key)", "relation_with": "Relation with:", "edit_connection": "Edit connection", "add_connection": "Add connection with another table", "constraints_section_title": "Constraints", "constraints": {"primary": {"label": "Primary", "description": "Indicates that a column, or group of columns, can be used as a unique identifier for rows in the table."}, "unique": {"label": "Unique", "description": "Ensures that the values ​​in the column are unique across all rows in the table."}, "required": {"label": "Required", "description": "Requires the column to always have a value. Does not allow null values ​​(NULL)."}, "auto_increment": {"label": "Auto Increment", "description": "Automatically numbers each new line."}}, "actions": {"create_fallback": "Create"}, "errors": {"update_failed": "Failed to edit column", "create_failed": "Failed to create column"}}, "FOREIGN_KEY_MENU": {"add_connection": "Add connection with another table", "labels": {"select_schema": "Select a Schema", "select_reference_table": "Select the reference table", "select_columns_for_reference": "Select columns from {tableName} to reference"}, "placeholders": {"select_schema": "Select a Schema", "select_table": "Select the table", "select": "Select"}, "headers": {"reference_table_fallback": "Reference Table"}, "buttons": {"add_column": "Add Column"}}, "HELP": {"help": "Help", "search": "Search", "what_is_your_doubt": "What is your doubt?", "how_to_use_template_package": "How to use the template package", "learn_with_our_step_by_step_guide": "Learn with our step by step guide", "learn_by_doing_together": "Learn by doing together", "learn_by_use_cases": "Learn by use cases", "explore_with_practical_examples_solved_in_mitra": "Explore with practical examples solved in Mitra", "faq": "FAQ", "check_out_our_help_playlist_on_youtube": "Check out our help playlist on Youtube", "related_content": "Related content", "our_community": "Access our community on Discord", "check_our_docs": "Check our complete documentation"}, "ONBOARDING": {"learn_by_doing": "Learn by doing", "learn_by_pratice": "Learn how to use Mitra to create your apps through practical examples", "create_database": "Easily create and manage your databases", "create_interface": "Design and build interfaces for your data", "create_functions": "Create functionalities for your components", "integrate_with": "Integrate Mitra with any platform or system", "launch": "Launch and scale your software effortlessly", "open_our_playlist": "Access our YouTube playlist", "create_tables": "Create tables", "youtube_playlist": "YouTube Playlist", "know_templates": "Know your template", "community": "Community", "talk_to_support": "Talk to support", "import_data": "Import data", "jdb_and_files": "JDBC and files", "security_and_profiles": "Security and Profiles", "version_and_publish": "Versioning and Publishing", "data": "Data", "customize": "Customization", "interaction": "Interactions", "create_database_actions": "Create database actions", "create_mitra_actions": "Create <PERSON><PERSON> actions", "create_screens": "Create screens", "config_components": "Configure components", "create_forms": "Create forms", "create_detail_modals": "Create record details", "docs": "Docs", "integrate": "Integration", "logic": "Logic", "lauching": "Launch", "courses": "Courses"}, "METADATA": {"attr_types": {"type": "Column type", "Date": "Date", "SingleLineText": "Text", "Number": "Number", "SingleChoice": "Single Choice", "MultiChoice": "Multi Choice", "Checkbox": "CheckBox", "LinkDimension": "Link to another table", "SecuritySingleLink": "Corporate security", "SecurityMultiLink": "Corporate security", "MultiLinkToUser": "Link to User", "SingleLinkToUser": "Link to User", "Dropdown": "Dropdown", "Colorpicker": "Colorpicker", "Toggle": "Toggle"}, "creator": "Developer"}, "DIMENSIONS": {"create_error": "Fail to create table", "delete_error": "Fail to delete table", "update_error": "Fail to update table", "data_entry_error": "Fail to add value", "attributes": {"dynamic_toggle": "Allow to insert a query with a dynamic condition, showing the results as if it were an attribute.", "duplicate_pk": "Unique attributes do not allow duplicate values", "change_type_attribute_error": "Faail to change attribute type", "add_attribute_error": "Fail to add attribute", "precision": "Precision", "add_name": "Add name", "add_precision": "Add precision", "add_option": "Add option", "add_dimension": "Add table", "select_dimension": "Select table", "multiple_selection": "Multiple selection", "delete_attribute": "Delete attribute", "delete_attribute_error": "Delete attribute error", "delete_dimension": "Delete table"}, "attribute_with_this_name_already_exists": "An attribute with this name already exists, please choose another one.", "your_cube_cannot_have_2_connected_dimensions": "Your cube cannot have 2 connected table."}, "COLLABORATORS": {"invite_send_error": "Fail to send invitiation", "invite_update_error": "Fail to update invitation", "invite_validate_error": "Fail to validate invitation", "delete_error": "Fail to delete collaborator", "update_error": "Fail to update collaborator", "already_contributor": "The user is already a contributor to this workspace", "already_invited": "Collaborator is already invited in this space", "already_creator": "{email} is already creator in this project", "invitation_user_different": "The invitation link does not match the logged-in user"}, "PLAN": {"free_credits_limit": "You have reached the limit of free credits.", "complimentary_credits": "The 5 complimentary credits have already been used.", "limit_alert_1": "You have reached the daily limit of your free plan.", "limit_alert_2": "Set up your Gemini key to continue developing.", "contract_plan": "Contract plan", "project_frozen": "FROZEN PROJECT", "payment_failed": "Payment failed", "payment_error": "There was an error processing your payment. Please try again or use a different payment method.", "until_day": "until the day", "observation": "Note: You will continue to use all the features of the plan", "cancel_subscription_warning": "You are about to cancel your subscription. This action will result in the loss of some benefits of your current plan. Are you sure you want to proceed?", "try_again_later": "Please try again later or contact support for more information.", "unavailable_content": "Oops! This content is temporarily unavailable", "size_descending": "<PERSON><PERSON> (Descending)", "size_ascending": "<PERSON><PERSON> (Ascending)", "used": "used", "increase_storage": "Increase storage", "storage_summary": "Database Storage Summary", "storage_limit_reached_message_v2": "Your storage limit has been reached, temporarily restricting some functionalities. To restore full access, review your current usage or consider a plan upgrade.", "storage_limit": "Storage limit reached.", "storage_limit_reached_message": "Your storage limit has been reached, and some functions are temporarily blocked. Review current usage to free up space or consider upgrading your plan to restore full access to functionalities", "storage_limit_reached": "Storage limit reached. Review your usage or upgrade your plan.", "edit_payment_method": "Edit payment method", "payment_info_correction_needed": "Payment information correction needed", "payment_not_processed": "Your payment method was not processed. To continue having access to the plan", "until_this_date": "until this date", "plan_change_warning": "You will be able to continue using all the features of the plan", "plan_change_notice": "Your plan will be changed to", "from_day": "From the day", "attention": "Attention", "plan_downgrade_warning": "You are opting to downgrade your plan to Starter. The new amount will be charged from the next invoice on November 16th. Until November 16th, you will continue to use all the features of the Scale plan.", "current": "Current", "cancel_subscription": "Cancel Subscription", "change_plan": "Change Plan", "manage_plan": "Manage subscription", "payment_failure_update_info": "Your payment method failed. Please update your payment information.", "payment_failure": "Payment failure", "paid": "Paid", "show_invoice": "View Invoice", "status": "Status", "value": "Value", "billing_date": "<PERSON> da Cobrança", "page": "Page", "next_payment_date": "Next payment date", "view_invoice": "Click on “View Invoice” to download", "invoice_history": "Invoice History", "credit_card_ending_in": "Credit card ending in", "method_payment": "Payment method", "card_helper": "By confirming your subscription, you allow Mi<PERSON> to charge you for future payments in accordance with the company's terms. You can cancel your subscription at any time.", "country_or_region": "Country or region", "due_date": "Due date", "card_number": "Card number", "card_name": "Name on Card", "payment_method": "Payment Method", "contact_email": "Contact E-mail", "add_promo_code": "Add promotional code", "tax": "Tax", "monthly_charge_quantity_1": "Quantity 1, Charged monthly", "per_application": "per application", "plan": "Plan", "permonth": "per month", "subscribe_plan": "Subscribe Plan", "payment_confirmation": "Payment Confirmation", "other_plans": "Other plans", "database": "Database", "attachments": "Attachments", "free": "Free", "actual_plan": "Actual plan", "be_an_early_adopter": "Be an early adopter and test for free", "enjoy_beta": "Take the opportunity to explore our beta version firsthand and help us shape the future of Mi<PERSON>", "daily_limit_reached": "Daily limit reached", "recommended": "Recommended", "start_now": "Start now", "call_sales": "Talk to sales", "developer": "Developer", "corporate": "Corporate", "build_saas": "Build SaaS solutions \n and sell to the community!", "build_corp": "Build custom corporate solutions \n for your company!", "yearly": "Yearly", "monthly": "Monthly", "monthly_by": "per month", "yearly_by": "per year", "yearly_bid": "Billed annually", "plans_and_prices": "Plans and prices", "you_are_on": "You are on", "free_trial": "Free trial", "trial_remaining": "trial days", "your_trial_period_ends_in_n_days": "Your trial period ends in {days} days.", "upgrade": "upgrade", "or_add_coupon": "add a coupon", "enjoy_all_benefits": "to enjoy all the benefits of <PERSON><PERSON>!", "by_application": "By application", "per_business_user": "per Business User", "per_developer_user": "por Developer User", "custom_features": "We can negotiate to better meet your custom requirements!", "free_forever": "Free forever", "features": {"development_version": "Development version", "database_lines": "{count} database lines", "database_size": "{size} of database size", "daily_loads": "{count} daily loads", "email_send": "{count} email sends per day", "all_features": "All functionalities", "unlimited_database_lines": "Unlimited database lines", "unlimited_developers": "Unlimited developers", "use_your_brand": "Publish application with your brand and domain", "publish_on_marketplace": "Publish on Marketplace", "attachments_size": "{size} attachments size", "backup_days": "Backup of the last {days} days", "daily_guest_access": "{count} access without login/ day", "free_templates": "Free templates", "support_outside_community": "Support outside the community", "up_to_users": "Up to {count} users", "own_domain": "Own domain", "unlimited_ai": "AI with unlimited requests", "sync_database": "Sync your database to use without storage limit"}, "plans": {"ENTERPRISE": "ENTERPRISE", "GROWTH": "SCALE", "SCALE": "SCALE", "FREE": "FREE", "STARTER": "STARTER", "TEAM": "TEAM", "enterprise_descr": "Ideal for maximum security and scalability", "growth_descr": "Ideal for scaling your application", "free_descr": "Ideal for learning and building", "starter_descr": "Ideal for launching your application", "team_descr": "Ideal for scaling your team and business", "pro_descr": "For small teams who want to centralize and optimize their operational processes", "corp_free_descr": "For individuals who want to learn and start using Mitra in their companies", "scale_descr": "Ideal for scaling your application", "corp_enterprise_descr": "Ideal for larger teams, with maximum security and scalability"}, "cpf_cnpj_mandatory": "CPF/CNPJ is mandatory", "name_card_mandatory": "Name on card is mandatory", "card_number_required": "Card number is required", "expiration_date_mandatory": "Expiration date is mandatory", "cvv_is_mandatory": "CVV is mandatory"}, "WORKSPACES": {"ai_settings": "{name} settings (AI)", "view_more_courses": "View More Courses", "view_all_documentation": "View All Documentation", "new_tag": "New!", "email_verification": "After registering an email, ", "email_verification_aws": "Amazon Web Services (", "email_verification_2": " will send a confirmation to it. Just open the email, ", "email_verification_link": "click on the confirmation link", "email_verification_3": " and that's it! Then, return to <PERSON><PERSON> and your email will be verified.", "verify_email": "Verify your email", "enter_mail_name": "Enter the e-mail name", "enter_mail": "Enter e-mail", "no_environment_start": "You don't have any Environment created yet. Let's get started?", "no_workspace_start": "You don't have any {name} Workspace created ?", "validating_domain": "Validating domain", "no_workspace_start_2": "yet. Let's get started", "target_workspace": "Target workspace", "no_interface": "No interface has been created yet", "no_action": "No action has been created yet", "remove_skill": "Remove skill", "settings_desc": "Manage your project members, your account permissions, and your app subscription.", "user_limit_reached": "User limit reached", "invalid_email": "Invalid E-mail", "trial_info": "Time in which your user will be able to test the tool.", "trial_time": "Trial Time", "monthly_info": "The amounts received will be allocated 70% to the creator of the solution and 30% to the Mitra platform.", "monthly_payment": "Monthly payment", "banner_title": "Banner de apresentação", "logo_info": "Update your solution logo.", "header_info": "Update your solution details here. This information will appear in the Marketplace.", "desc_info": "Describe your solution in up to 200 characters.", "tags_info": "Select up to 8 tags.", "name_info": "This name will be displayed both to users and on the marketplace.", "company_info": "You need to be registered as a partner company to publish your application.", "published_solution": "Published Solution", "input_create_workspace": "What will be the name of your Workspace?", "confirm_delete_project": "Are you sure you want to delete the Project? This action cannot be undone!", "created": "Workspace created successfully", "create_error": "Fail to create Workspace", "removed": "Workspace removed successfully", "remove_error": "Fail to remove Workspace", "configure_workspace": "Configure Workspace", "manage_members": "Manage members", "no_database": "You don't have any projects yet", "only_creator_manage": "Only creators manage this content", "manage_workspace": "Manage workspace", "delete_workspace": "Delete Workspace", "delete_workspace_title": "Are you sure you want to delete this workspace?", "delete_workspace_body": "This action cannot be undone.", "no_access": "You do not have access to this workspace", "invite_members_create_projects": "Invite members so they can create projects", "invite_users": "Invite User", "users_who_create_projects": "Users who create projects", "create_new": "Create workspace", "return_home": "Return to Home", "settings": " Settings", "settings_off": "", "account_type": "Account Type", "project_limit": "Project Limit", "purchased_users": "Purchased Users", "solution_site": "Solution Site", "enter_solution_site": "Enter the solution site", "unique_users": "Unique Users", "storage_files": "Storage | Files", "storage_bank": "Storage | Bank", "of": "of", "users": "users", "projects": "Projects | Project", "registration_date": "Registration Date", "bank": "Bank", "files": "Files", "file": "File", "developer_user": "Developer User", "business_user": "Business User", "developers": "Developers", "developer": "Developer", "uploads": "Uploads (Per day)", "uploads_day": "Uploads today", "accesses": "Accesses without Login (Per day)", "boradcasting": "Boradcasting (Per day)", "summary": "Workspace Summary", "name": "Name", "workspace_information_general": "General Information", "information": "Information", "user": "Users", "admins": "Administrators", "admin": "Administrator", "creators": "Creators", "subscriptions": "Subscriptions", "workspace_information": "Workspace Information", "add_image": "Add image", "my_team": "My team", "registered_users": "Registered users", "loads_today": "Loads (Today)", "loads": "Loads", "email": "E-mail (Today)", "entry": "Entry", "guest_entry": "Guest access today", "broadcastings": "Broadcastings", "create_projects": "Create projects", "edit_projects": "Edit Projects", "my_first_workspace": "My First Workspace", "edit_projects_single": "Edit Project", "role": "Role", "access_without_login": "Access without login (Today)", "deleting_your_workspace": "Deleting your Workspace is an irreversible action that will result in the complete deletion of your projects and data, with no possibility of recovery.", "new_user": "New user", "delete_project": "Delete Project", "sure_to_delete_the": "Are you sure you want to delete the ", "sure_to_remove_the": "Are you sure you want to remove the skill", "project": "project", "delete_workspace_warning": "This action cannot be undone and all data will be lost permanently. The associated subscription will remain active until the end of the billing cycle, with no refund for the unused period.", "this_action_cannot_be_undone": "This action cannot be undone and the data will be lost permanently", "have_reached_user_limit": "You have reached the limit of members allowed in the workspace.", "consider_a": "Consider a", "plan_upgrade": "plan upgrade", "or_manage_users_invite": "or manage your team's users to release more invites.", "this_action_is": "This action is", "irreversible": "irreversible", "and_will_result_in": "and will result in", "complete_deletion": "complete deletion", "your_projects_and_data": "of your projects and data,", "with_possibility_recovery": "with no possibility of recovery.", "publish_solution": "Publish solution", "publish_app": "Publish app", "publish_with_domain": "Publish with my domain", "publish": "Publish", "no_projects_published": "No projects published", "my_projects": "My projects", "my_workspaces": "My workspaces", "project_downloaded_customers": "Projects Downloaded By Customers", "published_in": "Published in", "in_production": "In production", "pending_approval": "Pending approval", "pending_company_approval": "Pending company approval", "new_version": "New version", "publish_your_domain_information": "To publish with your domain, we need some information:", "per": "per", "subscribe_free": "Subscribe (Free)", "subscribe": "Subscribe", "monthly_per_user": "Monthly / per User", "about": "About", "developed_by": "Developed by", "resources": "Resources:", "tutorial": "Tutorial", "developer_website": "Developer website", "terms_of_use": "Terms of use", "categories": "Categories: | Categorie", "free": "Free | Free", "user_month": "/user/month", "per_month": "/month", "you_dont_have_any_projects_created_yet": "You don't have any projects created yet.", "you_are_just_guest": "You are just a guest developer in this workspace.", "create_workspace": "Create a workspace", "free_plan": "Free plan", "member": "member", "upgrade_your_plan": "Upgrade your plan", "toggle_workspace": "TOGGLE WORKSPACE:", "paid": "Paid", "for_what_purpose_you_intend_to_use": "For what purpose do you intend to use it?", "want_to_create_customized_solutions": "I want to create customized solutions for my own operations.", "want_to_create_solutions_community": "I want to create solutions and sell them to the community.", "enter_project_name": "Enter project name", "select_the_category": "Select the category", "company": "Company", "enter_company_name": "Enter company name", "company_logo": "Company logo", "insert_the_company_logo_link": "Insert the company logo link", "description": "Description", "enter_description": "Enter description", "solution_home_screen_id": "Solution Home Screen ID", "enter_home_screen_id": "Enter Home Screen ID", "lock_cube_id": "Lock Cube Id", "enter_lock_cube_id": "Enter the Lock Cube ID", "lock_screen_id": "Lock screen id", "enter_lock_screen_id": "Enter the Lock screen ID", "tell_about_project": "Tell us a little about the project", "solution_logo": "Solution logo", "insert_solution_logo": "Insert the solution logo", "onboarding_video": "Onboarding video", "insert_onboarding_video_link": "Insert the onboarding video link", "promotion_video": "Video promotion", "insert_promotional_video_link": "Insert the promotional video link", "media_disclosure": "Image disclosure 1", "insert_promotional_media_link": "Insert the promotional image 1", "media_disclosure_2": "Image disclosure 2", "insert_promotional_media_link_2": "Insert the promotional image 2", "media_disclosure_3": "Image disclosure 3", "insert_promotional_media_link_3": "Insert the promotional image 3", "laod_data": "Load data", "chat_with_your_data": "Chat with your data", "hello": "Hello", "welcome_to_mitra": "welcome to <PERSON><PERSON>", "dont_have_projects_created_yet_title": "You don't have any projects created yet.", "dont_have_projects_created_yet_subtitle": "Click on one of the options below to get started.", "start_from_scratch": "Start from scratch", "start_with_templates": "Start with Templates", "search_screens": "Search screens", "search_modules": "Search modules", "artificial_intelligence": "Artificial Intelligence", "please_enter_project_name": "Please enter project name", "please_select_category": "Please select a category", "field_required": "This field is required", "please_enter_company_name": "Please enter company name", "please_insert_company_logo": "Please insert your company logo", "please_enter_description": "Please enter a description", "please_enter_home_screen": "Por favor, insira a tela inicial", "please_enter_information_about_project": "Please enter information about the project", "please_insert_logo_image": "Please insert logo image", "please_insert_onboarding_video": "Please insert the onboarding video", "please_insert_marketing_video": "Please insert marketing video", "please_insert_promotional_media_link_1": "Please insert the promotional media link 1", "please_insert_promotional_media_link_2": "Please insert the promotional media link 2", "please_insert_promotional_media_link_3": "Please insert the promotional media link 3", "main_subdomain": "Subdomain", "enter_main_subdomain": "Enter the Main Subdomain", "secondary_subdomain": "Secondary subdomain", "enter_secondary_subdomain": "Enter Secondary Subdomain", "email_sending_service": "Email to sending service", "enter_email": "Enter email", "enter_image_url": "Enter the image url", "enter_image_url_or_upload": "Enter the image url or upload the file", "solution_icon": "Solution icon", "uploader_type": " .jpg and .png here", "support_email": "Support Email", "enter_support_email": "Insira o e-mail de suporte", "support_name": "Support Name", "enter_support_name": "Enter support name", "number_trial_days": "Number of Trial days", "enter_number_trial_days": "Enter the number of Trial days", "monthly_value": "Monthly value", "enter_monthly_value": "Enter the monthly amount", "remove_user": "Remove user", "are_sure_to_remove_user": "Are you sure you want to remove the user", "specialties": "Specialties", "see_details": "See details", "add_skill": "<PERSON><PERSON>", "build_personalized_bia_profiles": "Build personalized AI profiles", "new_skill": "New Skill", "community": "Community", "one_project_per_base": "My project will be based on a client basis", "each_client_have_different_database": "Each client will have a different database", "you_have": "You have", "workspaces_developer_mode": "Workspaces in Developer Mode", "workspaces_enterprise_mode": "Workspaces in Enterprise Mode"}, "WHITE_LABEL": {"publishing": "Publishing", "save_version": "Save Version", "save_and_update": "Save and Update", "publishing_tip_01": "Your project is being published.", "publishing_tip_02": "Please wait while we finalize the process. It will be ready for use soon!", "to_add_custom_domain": "and add a custom domain.", "publish_your_project": " publish your project ", "click_to_publish": "Press Publish to ", "validating_domain": "Validating domain", "in_domain": " in domain", "not_published": "Not published", "published_and_optmized": "Project published successfully!  🎉", "unpublish": "Unpublish", "unpublish_all": "Unpublish all", "web_solution": "Web Solution", "mobile_solution": "Mobile Solution", "mitra_apps_terms": "By publishing on Mitra Apps, people will be able to see and use your solution through the “Mitra Apps” application. You will be able to launch your whitelabel application in the future, but this is a faster option to make your solution available to end users. Submissions must be approved by our team.", "images": "Images", "mitra_apps_publish": "Publish project on Mitra Apps", "images_tip": "Maximum 6 - size", "insert_video_link": "Insert the video link", "short_description": "Short description", "long_description": "Long description", "type_description": "Write your description here", "mitra_apps_hint": "Your project will be published in our Mitra App Store and can be used by users.", "app_name": "App Name", "base_domain": "Base domain", "custom_domain": "Custom domain", "project_publish_hint": "Project that will be published.", "domain": "Domain", "base_domain_info": "Domain automatically generated by the system for main access to your application.", "custom_domain_info": "Create a custom domain using <PERSON><PERSON>'s subdomain or connect to a domain you already own.", "get_mitra_subdomain": "Get a subdomain from Mi<PERSON>", "get_mitra_subdomain_hint": "Instantly connect a custom Mitra subdomain.", "connect_custom_domain": "Connect a domain you own", "connect_custom_domain_hint": "Connect a domain acquired through a hosting service.", "logo_hint": "The logo will be used on the login screen and navigation bar.", "primary_color": "Primary Color", "color_hint": "The main color will be used on the login screen and other native screens.", "user_permission": "End user releases", "home_screen": "Initial app screen*", "home_screen_hint": "This is the screen that opens by default when the user enters the application.", "enable_mobile": "Activate mobile version for this project", "display_project_on_mobile": "The project appears in the project list within the mobile", "project_settings": "Project settings", "published": "Published", "edit_published_app": "Edit published app", "default_profile": "Default profile", "default_profile_hint": "The default profile will be associated with all users when they register", "publish_project": "Publish project", "unpublish_project": "Unpublish Project", "already_in_use": "This domain is already in use by another project.", "invalid_domain": "Invalid domain"}, "DRIVE": {"storage": "Storage", "add_file_button": "Use the '+ Add File' button to start filling this folder with your documents.", "add_folder_button": "Use the '+ New Folder' button to create your first folder and start organizing your files.", "empty_folder": "This folder is empty", "size": "Size", "add_file": "Add file", "new_folder": "New folder", "my_folders": "My Folders", "delete_folder": "Delete Folder", "delete_file": "Delete File", "delete_folder_warning_1": "You are about to delete the folder", "delete_folder_warning_2": "All content will be lost and this action cannot be undone.", "delete_file_warning_1": "You are about to delete the file", "delete_file_warning_2": "This action is permanent and cannot be undone.", "confirm_continue": "Are you sure you want to continue?"}, "DATABASE": {"allows_public_screen": "Allows to be run from a public screen", "public": "Public", "add_table_placeholder": "Add_table", "date_format": "Date Format", "not_configured": "This table has not been configured yet, please change the query.", "not_column": "This is not a column in the current table. The data displayed here is part of another table that is connected to this one.", "input_variable": "Input Variables", "global_variables": "Global Variables", "global_variable": "Global Variable", "this_screen": "(On this screen)", "temporary_value_description": "Fill in a temporary value to test different scenarios. It will not be stored.", "native_variables": "Native Variables", "no_variables_found": "No variables found.", "variable_already_exists": "Variable already exists", "temporary_value": "Temporary value", "native_table": "Native Tables", "only_fist_value": "For this input, only the value of the first row and first column will be considered.", "sum": "Sum", "selector": "Selector", "average": "Average", "count": "Count", "default_aggregation_function": "Default aggregation function", "registrations": "Keys", "script_not_execulted": "The script will not actually be executed, just tested!", "not_allowed_script": "It is not allowed to run a Script in a Query. To do this, create a new Script.", "not_allowed_query": "It is not allowed to run Query in a Script. To do this, create a new Query.", "entity": "Entity", "view_table": "View Table", "edit_table": "Edit Table", "may_take_while": "This action may take a while and may require adjustments to some of your queries.", "wish_continue": "Do you wish to continue?", "recreate_database": "Recreates your database metadata, bringing version updates and adjusting your VIEWs.", "test": "Test", "run": "Run", "refresh": "Refresh", "import_csv": "Import CSV", "invalid_script": "Invalid script", "validated_script": "The script is valid", "alowed_scrip": "Only the tables that can be scripted appear in this list.", "script_context": "Script Context", "version_warning": "When customizing the attribute name, a new version will be created for the Registration.", "customize_attribute_name": "Customize attribute name", "path": "Path", "lots_of_content": "More than 10 columns", "data": "Data", "sure_unlink_dimension": "Are you sure you want to unlink your table", "in_cube": "in the cube", "change_query": "Change query", "clear_cube": "Clear cube", "clear_dimension": "Clear table", "type_search": "Type to search", "clean_cube": "Clean this cube", "clean_dimension": "Clean this table", "sure_clean_cube": "Are you sure you want to clean the cube", "sure_clean_dimension": "Are you sure you want to clean the table", "all_data_cube_deleted": "All data in this cube will be deleted", "all_data_dimension_deleted": "All data in this table will be deleted", "dynamic_attribute": "Dynamic Attribute", "add_dimension": "Add registration", "enter_name": "Enter a name", "select_dimension": "Select Table", "add_dimension_to_cube": "Add records to the cube", "data_dictionary": "Data dictionary", "escape_all": "Escape ALL", "escaped_dimension": "Escaped table", "used_cube": "The cube is being used in screen components. Please remove before deleting!", "used_dim": "The table is being used in screen components. Please remove before deleting!", "no_description": "No description", "no_registration": "No table available.", "start_connect_record": "Start by creating a record to connect to another record.", "duplicate_id": "Duplicate ID", "no_attribute": "No ", "version": "Version", "new_version": "New version", "main_version": "Main version", "main_db": "Main database", "cube_versioning": "Cube versioning", "auto_incremental": "Auto increment", "date": "Date", "numeric": "Numeric (Integer)", "text": "Text", "group": "Group", "exhibition": "Exhibition", "code": "Code", "both": "Both", "result": "Result", "invalid": "Invalid", "ordination": "Ordination", "column_value_type": "Column value type", "dynamic_cube": "Dynamic cube", "configure_query": "Configure query", "run_query": "Run Query", "close_all_tabs": "Close all tabs", "auxiliary_table": "Auxiliary table", "query_message": "Configure and enter a query to view data.", "last_execution": "Last import", "last_execution_rows": "row on | rows on", "data_loader_log": "Import log", "data_loader_not_have_logs": "This import has no log records at the moment.", "sync_in_progress": "Synchronization in progress", "data_list": "Data list", "analysis_dataset": "Analysis Dataset", "welcome_database": "Welcome to the Database", "select_data": "Select or create database objects", "existing_data": "Select existing data", "enlarged_view": "Expanded view", "encrypt": "Encrypt", "log": "Log", "new_group": "New group", "hide_in_drill": "Remove from drill", "alert_lose_data": "All cube content will be lost. Do you want to continue anyway?", "only_colarborators": "You do not have access to this database!", "create_error": "Fail to create project", "remove_error": "Fail to remove project", "open_error": "Fail to access project", "new_table": "New table", "table": "Table | Tables", "new_query": "New query", "query": "Query", "foreign_key": "Composite Primary Key", "simple_key": "Simple key", "create_dynamic_cube": "Dynamic Cube", "type": "Type", "select": "Select", "register": "Table", "cube": "C<PERSON>", "dimension": "Table", "add_entries_to_cube": "Add entries to the cube", "insert_nickname": "Insert nickname", "find_record": "Find a record", "edit_attribute": "Edit Attribute", "new_attribute": "New Attribute", "unique_key": "Unique key", "color": "Color", "choose_option_color": "Choose option color", "more_colors": "More colors", "option": "Option", "new_option": "New option", "select_the_registration": "Select the table", "add_new_dimension": "Add new table", "add_new_jdbc": "Add new JDBC", "new_dimension": "New table", "allow_linking_multiple_records": "Allow linking to multiple records", "dimension_type": {"numeric": "Numeric", "text": "Text", "date": "Date", "single_selector": "Single selector", "multi_selector": "Multi selector", "connect_another_record": "FK", "corporative_security": "Corporative security", "automation": "Automation"}, "automation": {"automation_bg": "Background color", "automation_font": "Font color", "automation_bold": "Bold", "automation_prefix": "Prefix", "automation_sufix": "Suffix"}, "add_row": "Add row", "add_column": "Add column", "sure_to_delete_script": "Are you sure you want to delete script", "sure_to_delete_query": "Are you sure you want to delete query", "sure_to_delete_action": "Are you sure you want to delete action", "sure_to_delete_free_db_online_table": "Are you sure you want to delete online table", "sure_to_delete_data_loader": "Are you sure you want to delete the import", "sure_to_delete_dimension": "Are you sure you want to delete table", "sure_to_delete_attribute": "Are you sure you want to delete attribute", "table_sync_data": "Your \"{name}\" table, already synchronized, will have its data reset.", "sure_to_delete_cube": "Are you sure you want to delete cube", "action_cannot_undone": "This action cannot be undone.", "records": "Records", "record": "Record", "description": "Description", "sure_delete": "Are you sure you want to delete the ", "destination_cube": "Destination Cube", "cube_bound": "Cube that bounds intersections of calculations", "visual_customization": "Visual Customization", "select_or_create": "Select or create", "general": "General", "first_option_in_the_list_is_default": "The first option on the list is the default", "automatically_created_item": "This item is automatically created to be the default option when this table is referenced in a “Connect to another record” attribute", "hint_text_character": "You can enter up to {max} characters.", "characters_remaining": "{remaining} characters remaining", "you_text_hit_max_characters": "58 character limit reached.", "table_name": "Name", "cube_name": "Cube name", "we_are_loading_your_data": "We are loading your data", "meantime_your_can_browse_other_tables": "In the meantime you can browse other tables.", "preview_not_available": "Preview is not available!", "data_too_large_to_display": "The data in this table is too large to display here. You can still use it normally in your Dashboards.", "filter_variables": "Filter Variables", "query_insert_table": "Insert table", "query_insert_here": "Insert here", "query_insert_variable": "Insert variable", "query_error_message": "Only INSERT, UPDATE or DELETE operations are allowed.", "variable": "Variable", "content": "Content", "title": "Title", "on_this_screen": "It on this screen?", "view_table_msg": "View tables include descriptions of FKs, but may reduce performance. Use them only when necessary.", "add_table": "Add table", "column_are_metrics": "Columns that are metrics", "online_table": "Online Table", "attribute": "Attribute", "metric": "Metric", "new_metric": "New Metric", "this_table_has_not_configured": "This table has not been configured yet.", "no_results": "No results", "free_db": "Bank Analytics", "want_to_use_online_table": "I want to use my online tables", "jdbc_error": "Oops, something went wrong!", "jdbc_error_message": "We are unable to connect to the selected bank at this time. It may be offline or experiencing instability. Please try again later or select another bank to continue.", "online_table_error": "Please check the query and JDBC status before continuing.", "synchronize_project_db": "Synchronize the project to use an external database with the new main development base of your system.", "soon_allow_external_db": "Soon, we will allow you to use external databases as your primary database in Mitra. For now, you can connect to external databases using \"Online Table\" or \"Import\".", "connect_external_db": "Connect to an external database to perform Data Loads or create an Online Table, without changing the project's main database.", "configure_table_policy": "Configure Table Policy", "online_tables": "Online Tables", "url_file": "URL File", "imports": "Imports", "synchronize": "Synchronize", "query_preview": "Query Preview", "imported_table_preview": "Imported Table Preview", "successfully_synchronized_table": "Table `{tableName}` synchronized successfully.", "no_spaces_allowed": "No spaces allowed.", "edit_data_loader": "Edit Import", "create_database_action": "Create Database Action"}, "AI": {"ai_title": "AI", "show_old_posts": "View more old posts", "executing_plan": "Executing Plan", "planning": "Planning", "few_minutes": "This may take a few minutes...", "creating_project": "Creating your project", "failed": "Failure", "create_financial_management_project": "Create a financial management project", "try_to_fix": "click here to fix", "watch_and_learn": "Watch and learn", "analyze_all_components": "Analyze all system screens, not just the current one.", "fail_to_send": "Failed to send prompt, try again.", "key_connected": "Key connected successfully", "key_removed": "Key removed successfully", "ai_mode_builder_hint": "Builds systems from scratch, including tables, processes, screens, and actions. Takes longer to respond.", "ai_mode_analyst_hint": "From the created system, it is able to build analyses, has greater speed.", "config_key_tip_1": "We use the Gemini Pro 2.5 model, provided by Google.", "config_key_tip_2": "Register an API key to use {name} — it will be used by you in all your projects and workspaces when the use of your own key is enabled.", "config_key_tip_3": "This option uses your key and avoids consuming workspace credits, but your personal key may reach the usage limit.", "config_key_tip_4": "Register an API key to activate {name} in this workspace — it will be shared among all projects and users.", "config_key_tip_5": "To disable this option, an administrator needs to configure an API key for the workspace.", "account_settings": "account settings", "daily_credits": "Daily Credits", "use_free_plan": "You are using the free plan", "do_upgrade": "Upgrade for unlimited credits", "no_chats": "No chats", "insert_key_instruction": "Enter your key to continue using {tool_name}.", "what_you_want_to_create": "What do you want to create?", "prompt_placeholder": "Type what you want to create", "simple_analysis": "Quick Analysis", "simple_analysis_description_mitra": "{name} responds in about 10 seconds, ideal for objective queries that do not require comparative analysis, calculation generation, or advanced filters.", "simple_analysis_description_sk": "AI Copilot responds in about 10 seconds, ideal for objective queries that do not require comparative analysis, calculation generation, or advanced filters.", "simple_analysis_examples": "Examples:\n 1. Who generated the most margin for me this year?\n 2. How much revenue did I have per month?\n 3. What was my EBITDA per company last month?", "complex_analysis": "Advanced Analysis", "complex_analysis_description_mitra": "In advanced mode, {name} can think for up to 2 minutes, but can respond with complex calculations and filters, as well as personalized indicators.", "complex_analysis_description_sk": "In advanced mode, AI Copilot can think for up to 2 minutes, but can respond with complex calculations and filters, as well as personalized indicators.", "complex_analysis_examples": "Examples:\n 1. How is my turnover?\n 2. Which products had a reduction in average ticket compared to last year? And in % margin?\n 3. How many customers per salesperson have not purchased in over 120 days?", "new_chat": "New Chat", "ai_chats": "Ai chats", "ai_chat": "Ai chat", "new": "New", "search": "Search...", "edit_key": "Edit Key", "use_own_key": "Use my key", "send_new_message": "Send a new message...", "o1_mini_tooltip": "Activate optimized version, 50% faster and more economical", "to_start_configure_key": "To start, configure your key!", "configure_key": "Configure key", "do_not_know_where_start": "Do not know where to start?", "know_me": "Know me", "how_did_arrive_this_result": "How did I arrive at this result?", "create_dashboard_seconds": "Create a dashboard in seconds with", "ai_know_me": "Know me", "ai_dashboard_text_1": "Our Artificial Intelligence can transform your idea into a dashboard instantly. Just say which metrics and perspectives you would like to be on your dashboard. Click the button", "ai_dashboard_text_2": "to see what data this skill can use", "ai_dashboard_placeholder": "I want a sales and contribution margin dashboard, including a view by salesperson", "ai_dashboard_text_alternative": "Our Artificial Intelligence can transform your idea into a dashboard instantly. Just tell us which metrics and perspectives you would like were on your dashboard.", "chat_with_your_data": "Talk to your data", "quick": "Fast", "advanced": "Advanced", "hi_i_am": "Hello, I'm", "hi_i_am_ai": "Hello, i'm {name}!", "you_have_access_to_profiles": "You have access to the profiles:", "can_answer_you": "I can answer you", "this_makes_me_able_answer": "This makes me able to answer you", "questions_about_following_indicators": "questions about the following indicators:", "with_following_perspectives": "With the following perspectives:", "if_missing_indicator_talk_administrator": "If you are missing an indicator or perspective that is important to you, please talk to your application administrator.", "choose_skill_start_chatting": "Choose a skill to start chatting", "choose_project_start_chatting": "Choose a project to start chatting", "no_skills_found": "No skills found", "no_projects_found": "No projects found", "do_not_have_profile_configured": "You do not have a profile set up, contact an administrator to set it up.", "do_not_have_key_configured": "You do not have a key configured, contact an administrator to configure it.", "to_start_create_profile_and_key": "To get started, create one or more access profiles and configure your key!", "to_start_create_chat": "To get started, create a chat!", "configure_profiles": "Configure profiles", "please_bring_your_own_key": "Connect your {model} key", "please_bring_your_own_key_tip": "{platform} uses the Gemini Pro 2.5 model, provided by Google. To use {aiName}, configure your Gemini key below", "please_bring_your_own_key_tip_2": "— it will be used by you in all your projects and workspaces, and can be managed in the", "requirements_tier_1": "Requirement: The key must be of Tier 1 level", "profile": "Profile | Profiles", "configure": "configured | configured", "key_configured": "Key configured", "optimized_version": "Optimized version, 50% faster and more economical", "robust_version": "Robust version, with more complex reasoning", "try_ask_again": "Try asking again.", "last_updated_entries": "Last updated entries", "update_now": "Update now", "analysis_model_want_to_use": "Now select the analysis model you want to use:", "start_conversation": "Start conversation", "hi_i_am_bia": "Hello, I'm <PERSON>pi<PERSON>!", "hi_i_am_lila": "Hello, I'm <PERSON>!", "you_are_in_mode": "You are in the mode", "quick_analysis": "Quick Analysis.", "advanced_analysis": "Advanced Analysis.", "i_am_trained_respond_based_on": "I am trained to respond based on available data.", "this_skill": "in this skill", "this_project": "In this project", "which_are": "which are:", "i_cannot_perform_calculations_comparisons": "I cannot perform calculations and comparisons between this data in the mode", "simple_analysis_2": "Simple Analysis", "if_want_to_create_more_complex": "If you want me to create more complex indicators and views with comparisons, create a new chat and choose the", "made_complex_analysis": "made for complex analysis.", "if_i_am_unable_answer_your_question": "If I am unable to answer your question, it may be that the data requested is not among those I have available.", "remember_check_my_answers_using_button": "Remember to check my answers using the button", "because_ais_still_have_limitation": "because AIs still have limitations and can make mistakes!", "reflection_time_can_2_minutes": "My reflection time can be up to 2 minutes, but I can answer complex questions as long as they are contained within the data I listed above.", "i_can_answer_your_questions_about": "I can answer your questions about the following indicators:", "remember_check_answers": "Remember to check my answers using the “how did I get this result” button, as AIs still have limitations and can make mistakes!", "couldnt_find_data": "Oops, I couldn't find the data you asked for.", "have_the_skill'": "I have the skill", "have_the_project": "I'm with the project", "can_answer_about": "and I can answer you about the following data:", "and": " and ", "you_want_use": "If you want to use", "create_new_chat": "create a new chat!", "another_skill": "another skill", "another_project": "another project", "remember_that_was_trained_answer": "Remember that I was trained to answer questions about your data, I will have more soon", "projects": "projects", "as_the_ability_to_design": "as the ability to design, correlate data and suggest actions!", "connect_with_open_ai": "Connect with OpenAI", "please_bring_your_own": "Please bring your own OpenAI key to use {name}.", "enter_your_key": "Enter your key", "connect": "Connect", "get_your_key": "To get your key, go to the website", "get_your_key_2": "To get your key, go to the website", "view": "View", "table": "table", "graphic": "graphic", "full_screen": "full screen", "create_dashboards_seconds": "Create Dashboards in Seconds", "executing_interaction": "Executing plan...", "ai_m1": "Analyzing your request...", "ai_building_plan": "Building plan...", "ai_m2": "Preparing consultation...", "ai_m3": "Generating additional insights...", "ai_m4": "Almost everything ready...", "ai_m5": "Creating textual analysis...", "ai_m6": "It will take me a little longer because I am checking the query I made...", "ai_m7": "No data was found for your query, so I'll try again...", "ai_m8": "I'm searching for more information... this may take a little while...", "new_name": "New name", "create_chat_start_conversation": "Create a chat to start a conversation", "last_updated": "Last updated", "bia_settings": "AI Copilot Settings", "lila_settings": "<PERSON>", "oops_looks_like_youre_out_credits": "Oops! Looks like you're out of credits.", "out_credits_descr": "Add credits to continue using <PERSON> and access all available features.", "credits": "Credits", "add_credits": "Add credits", "to_continue_using_lila": "to continue using Lila.", "add_credits_web_version_lila": "Add credits to the web version to continue using <PERSON>.", "acess_settings_pending": "Access settings pending!", "contact_administrator_set_up": "Contact an administrator to set up.", "run_universal_tool": "Execute plan", "edit_universal_tool": "Edit plan", "recover_status": "Recover Status", "perform_interactions": "Perform interactions", "additional_settings": "Additional Settings", "additional_considerations_for_chatting": "Additional considerations for chatting with", "enter_considerations_here": "Enter additional considerations here", "enter_considerations_here_body_1": "Use this field to add information and considerations that will be concatenated with end-user prompts. These guidelines can help ", "enter_considerations_here_body_2": "to provide more specific and relevant answers based on the instructions provided.", "enter_a_value": "Please enter a value", "minimum_value_is": "The minimum value is R$", "mitra_uses": "Mi<PERSON> uses ", "by_google": ", model provided by Google.", "connect_key_app": "Connect your own Gemini API key to utilize our AI", "free_request": "25 free requests per day.", "key_level_required": "key level required:", "how_works": "Understand how it works", "how_get_key": "How do I get my key?", "how_generate_key": "Here you will learn how to generate your key and configure it to meet the Tier 1 requirement.", "how_to_generate_key": "How to generate your key", "enable_tier_1": "Enable Tier 1 Level", "problems_gemini_key": "Problems with your Gemini key", "how_it_works": "Understand how it works", "how_to_use": "Learn how to use Mitra to its full potential through AI.", "how_mitra_use_gemini": "How <PERSON><PERSON> uses Gemini", "mitra_uses_model_intro": "<PERSON><PERSON> uses the ", "gemini_pro_2_5": "Gemini Pro 2.5", "provided_by_google": " model provided by Google.", "ensure_autonomy_intro": "To ensure more autonomy and scalability, each user connects their ", "own_api_key": "own Gemini API key", "of_gemini": ".", "connection_allows_intro": "This connection allows for", "requests_per_day": "25 requests per day", "on_gemini_pro_2_5": ", for new accounts, access to ", "free_credits_amount": "$300 in free credits", "from_google_valid_for_90_days": " from Google, valid for 90 days.", "plans_how_it_applies_title": "How this applies to <PERSON><PERSON>'s plans:", "plan_free_label": "Free Plan", "plan_free_details_part1": " — Access to the full-stack environment with database and page builder, with ", "plan_free_requests": "10 AI requests per day", "plan_free_details_part2": ". Cloud costs are covered by us.", "plan_starter_label": "Starter Plan", "plan_starter_details_part1": " — ", "plan_starter_unlimited_ai": "Unlimited AI usage on our platform*", "plan_starter_details_part2": " + Web publishing with own branding and apps on Apple and Google stores.", "simple_logic_emoji": "💡 ", "simple_logic_title": "The logic is simple:", "simple_logic_explanation_text": " you bring the power (the <PERSON> key), and <PERSON><PERSON> provides the structure to transform this into complete web and mobile applications.", "paid_plan_gemini_limit_note": "* Even with a paid plan, the Gemini API key you are using may have its own limits defined by Google, such as daily or monthly quotas.", "access_the_intro": "Access ", "google_ai_studio_link_text": "Google AI Studio", "login_with_google_account_part1": " and log in with your Google account. After logging in, click on ", "get_api_key_button_text": "Get API Key", "login_with_google_account_part2": ". Then, click on ", "create_api_key_button_text": "Create API key", "key_generation_instructions_part1": ". The key will be generated automatically, and you can copy it. Just click on ", "copy_button_text": "Copy", "key_generation_instructions_part2": " to copy the key and paste it where needed.", "to_access_key_intro": "To access the ", "tier_1_level_text": "Tier 1 Level", "in_gemini_access_intro": " key in Gemini, go to ", "configure_billing_text": "Set up billing", "in_section_intro": " in the ", "api_plan_billing_section_text": "API Plan Billing", "and_register_payment_method_part1": " section and register a payment method. The data is used only to verify your identity — ", "no_automatic_charges_text": "no charges will be made automatically", "and_register_payment_method_part2": ". You will only be charged if you activate a paid plan or use prepayment. Upon completion, you receive ", "free_credit_amount_tier1": "$300 in free credit", "valid_for_intro": ", valid for ", "valid_for_90_days_text": "90 days", "remains_available_text": ", which remains available even without activating a plan.", "validate_key": "Validate key", "key_doesnt_fit": "Your key does not meet the Gemini 2.5 requirements. Please check and try again!", "start_experience_lila": "Start your experience with the new", "execute_function": "Execute function", "for_ai_work": "For the new AI to work, we made a structural change to the automatically generated views. Therefore, before activating it, we need to run a function that removes the FK descriptions in CAD_ and CC_, and this may require maintenance on your project's database objects. Click the copy icon to check if your project needs any maintenance.", "error_processing_ai_response": "Error processing AI response data", "unable_process_ai_response_data": "Unable to process AI response data. Please check that the response is in the correct format.", "upload_files_extensions": "Upload files (images, documents, code)", "retrying_with_countdown": "Attempt {1} of {2} ({0}) - Next in {3}s", "retrying_processing": "Attempt {1} of {2} ({0}) - Processing...", "typing_placeholder_1": "Create a system to manage sales funnels, with opportunities and contacts...", "typing_placeholder_2": "Create a vacation request and approval flow for employees...", "typing_placeholder_3": "Create a dashboard to visualize sales metrics (KPIs) with charts and filters...", "typing_placeholder_4": "Create a financial management system for small businesses with cash flow..."}, "ACTIONS": {"welcome_to_actions": "Welcome to Actions!", "select_existing_action": "Select an existing action or create a new one!", "test_call": "Test call", "call_preview": "Call Preview:", "call_type": "Call type", "name": "Name", "value": "Value", "i_dont_know_reference": "I don't know the reference", "preview_answer": "Preview of the answer:", "click_button_to_test_call": "Click the button to test the call and generate a \n preview of the response.", "there_were_no_headers": "There were no headers created.", "request_without_body": "Request without body.", "with_con": "With cron", "without_con": "Without cron"}, "TIME": {"time_now": "Now", "time_today": "Today", "time_yesterday": "Yesterday", "time_before_day_yesterday": "Day before yesterday", "time_3_days_ago": "3 days ago", "time_last_7": "Previous 7 days", "time_last_15": "Previous 15 days", "time_last_30": "Previous 30 days", "time_last_month": "Previous month", "time_more_60_than_days": "More than 60 days ago", "time_ago_specific": "{diffInDays} days ago", "time_january": "January", "time_february": "February", "time_march": "March", "time_april": "April", "time_may": "May", "time_june": "June", "time_july": "July", "time_august": "August", "time_september": "September", "time_october": "October", "time_november": "November", "time_december": "December", "time_last_year": "Last year"}, "MEMBERS": {"invite_business_user": "Publish the project before inviting a Business user.", "invite_new_user": "Invite New User", "share_link": "Copy the link below to share it with the user:", "invite_success": "Invitation successfully generated!", "coming_soon": "Coming soon", "notify_user_email": "Notify the user via email about this invitation", "user": "user | users", "members": "Members | Member", "invite_members": "Invite members to the project", "invite_workspace_members": "Invite members to the project", "project_members": "Project members", "workspace_members": "Workspace members", "search_member": "Search a member", "base_access": "Project access", "workspace_access": "Workspace access", "inivitation_sent": "Invitation sent", "members_access": "Members & Access", "members_access_subtitle": "Manage your project members and their account permissions.", "corporative_security": "Corporative security", "add_corporative_security": "Add corporative security", "new_member": "New member", "delete_corporative_security": "Delete corporative security?", "delete_corp_body_1": "Are you sure you want to delete corporate security", "delete_corp_body_2": "This action cannot be undone.", "delete_member_title": "Are you sure you want to delete this member?", "delete_member_body": "This action cannot be undone.", "screens": "Screens", "no_description": "No description", "profiles": "Profiles", "data": "Data", "edit_profile": "Edit profile", "create_profile": "Create profile", "group": "Group", "PROFILES": {"title": "Profiles", "new": "New Profile"}, "action": "Action", "dont_have_profile_yet": "You don't have a profile yet", "create_profile_different_types_users": "Create profiles to give personalized access to different types of users.", "tiny_profile": "profiles"}, "NATIVE": {"natives": "Natives", "cubes": "Cubes", "dimensions": "Tables", "connections": "Connections", "actions": "Actions", "connectors": "Connectors", "details_modal": "Record Details", "forms": "Forms", "users": "Users", "mask": "Mask", "identity": "Identity", "executables": "Executables"}, "PAGEBUILDER": {"update_excluded_html": "List of component IDs you do not want to be updated.", "update_update_html": "List of component IDs you want to update.", "update_all_html": "If true, updates all screen components.", "query_variable": "query variable", "for_modal_action": "to show action modal", "variable_reactivity": "Update components listed in manual reactivity", "select_existing_item": "Select or create new web screens, \n mobile screens, and forms!", "welcome_to": "Welcome to", "search_options": "Search options", "nickname": "Nickname", "dimension": "Table", "save_editions": "Save editions", "edition": "Edit", "document": "Document", "native_codes": "Native Codes", "log_of_entire_object": "//log of the entire object", "log_of_entire_view": "//log of the entire view", "log_of_first_row": "//log of the first row", "log_of_first_item": "//log of the first item of the first row", "component_view_matrix": "Component view matrix", "action_id": "Action ID", "for_floating_modal": "for floating modal", "to_close_reload": "to close on reload", "source_line_index_selection_interactions": "source line index, to apply selection in interactions", "modal_without_applying": "//modal without applying selection.", "modal_with_list_row": "modal with list row selection", "modal_where_index_is_source": "modal where index is the source line (0 is line 1)", "same_call_above_mandatory": "same call as above, but only with the mandatory attributes.", "content_id_record": "contentId is the ID of the record you want to select", "detail_index_source": "//detail where index is the source line (0 is line 1).", "detail_id_record": "//detail based on the ID of the record you want to edit.", "action_without_applyng": "//action without applying selection.", "action_where_index": "//action where index is the source line (0 is line 1).", "immediately_call_code": "immediately calls the next code.", "calls_return_action_mitra": "calls on return from actionMitra.", "or": "or", "call_next_code": "call next code when actionMitra finishes.", "addition_form": "//addition form.", "editing_form_index": "//editing form where index is the source line (0 is line 1).", "editing_form_id_registration": "//editing form based on the ID of the registration you want to edit.", "return": "//return", "created_or_changed": "that was created or changed, or null if the modal was just closed.", "call_return_form_mitra": "calls on return from formMitra.", "call_form_mitra_finishes": "call next code when formMitra finishes.", "db_action_without_selection": "//dbAction without applying selection.", "db_action_index": "//dbAction where index is the source line (0 is line 1).", "calls_return_db_action": "calls in the return of dbactionMitra.", "call_next_code_db_action": "call next code when dbactionMitra finishes.", "variable_name": "Variable name", "variable_content": "Variable content", "call_next_code_set_variable": "call next code when setVariable <PERSON><PERSON> finishes.", "calls_return_set_variable_mitra": "calls in the return of setVariableMitra.", "returns_query_value": "//returns the query value.", "call_next_code_query_mitra": "call next code when query<PERSON><PERSON><PERSON> finishes.", "calls_return_query_mitra": "calls in the return of query<PERSON><PERSON><PERSON>.", "update_mitra_components": "You can request to update all components on the screen, or just some components whose ID is provided. If the component has the 'updateMitra' function, instead of recreating the component, it calls the method.", "add_method_descr": "You need to add the method with this name 'update<PERSON><PERSON><PERSON>' in the root of the script for it to work, this makes that instead of the component updating, it calls this method. It serves to manually control the updates, and avoid blinking, or falling into the initial state again.", "when_mitra_updates_descr": "When the Mitra component updates, instead of updating the entire component, it will call this method only.", "upload_mitra_descr": "The method saves the file on the server and returns the path of the saved file.", "upload_att_descr": "File to be sent to <PERSON><PERSON>'s drive."}, "ACTION": {"step_refresh_screen": "Refresh", "step_go_to_screen": "Go to Screen", "step_clear_cube": "Clear attribute", "step_go_to_module": "Go to Module", "step_go_to_group": "Go to Group", "step_conditional": "Conditional", "step_save_data_entry": "Save Data Entry", "step_backup": "Backup", "step_reset_backup": "Restore Backup", "step_wait": "Wait", "step_show_message": "Show Message", "step_call_action": "Call Action", "step_selection": "Filter", "step_apply_selection": "Apply Filter to Current Screen", "step_reset_selection": "Reset All Filters", "step_reset_tree_selections": "Reset Filters in the Tree", "step_selection_interactivity": "Interactive Filter", "step_selection_cube": "Filter with attribute", "step_selection_save": "Save Current Filter", "step_selection_recovery": "Retrieve <PERSON>", "step_data_flow": "Calculation", "step_clean_dimension": "Clear table", "step_tree_reader": "Tree Reader (Deprecated)", "step_cube_reader": "<PERSON><PERSON> (Deprecated)", "step_extract_tree": "Extract Tree", "step_extract_dimension": "Extract table", "step_extract_cube": "Extract attribute", "step_extract_all_dimensions": "Extract All tables", "step_extract_all_cubes": "Extract All attributes", "step_extract_source": "Extract Source", "step_send_emails": "Send emails (Deprecated)", "step_selection_by_cube": "Filter by attribute", "step_write_back": "Write Back", "step_export_pdf": "Export to PDF", "step_execute_jar": "Execute JAR", "step_change_relationship": "Change relationship", "step_recalculate_automation": "Recalculate automation", "step_run_connection": "Run connection", "step_edit_register": "Create/Change table", "step_send_emails_migration": "Send emails", "step_send_emails_migration_call_api": "Call API", "step_logout": "User logout", "step_download_pdf": "Download PDF", "step_end_action": "End Action", "need_chips": "You need to insert the chips to be able to create/change records", "create_action": "Create action", "action_name": "Action name", "group_name": "Group name", "step": "Step", "called_step": "Called Step", "query_result": "Query result"}, "DETAILBUILDER": {"detail_modal": "Record Details", "delete_form": "Delete Form", "delete_form_body_1": "Are you sure you want to delete the form", "delete_form_body_2": "This action cannot be undone and all data referring to the form will be deleted!", "delete_detail_body_1": "Are you sure you want to delete the detail", "delete_detail_body_2": "This action cannot be undone and all data referring to the detail will be deleted!", "delete_detail": "Delete Detail", "delete_action_body_1": "Are you sure you want to delete the action", "delete_action_body_2": "This action cannot be undone and all data referring to the action will be deleted!", "delete_action": "Delete Action", "username": "Username", "field_config": "Configure field", "field_name": "Field name", "new_button": "New button", "view": "View", "settings": "Settings", "add_field": "Add field", "fields": "Fields", "choose_type_content_to_create": "Choose the type of content you want to create:", "screen": "Screen", "form": "Form", "new_field": "New field", "edit_field": "Edit field", "add_button": "<PERSON>d <PERSON>", "edit_button": "<PERSON>", "personalize": "Personalize", "title": "Title", "insert": "Insert", "tip": "Tip", "blocking_criteria": "Blocking criteria", "select": "Select", "type_of_execution": "Type of execution", "close_modal": "Close modal on reload", "float_modal": "Floating modal", "close_details_modal": "Close details modal on reload", "delete_button": "Delete Button", "win": "Win", "complete": "Complete", "lose": "Lose", "delete": "Delete", "file": "To file", "stand_by": "Stand by", "icon": "Ícone", "background_color": "Background color", "text_icon_color": "Text/icon color", "action_executor": "Action executor", "open_screen": "Open screen", "action": "Action", "step_edit_register": "Create/Change table", "edit_view_query": "Configure SQL", "module": "<PERSON><PERSON><PERSON>", "height": "Height", "width": "<PERSON><PERSON><PERSON>", "show_screen_popup": "Show screen as popup", "no_data_available": "No data available", "modal_height": "Modal height", "modal_width": "Modal width", "delete_field": "Delete field", "delete_field_title": "Delete field?", "delete_field_body": "This action will directly impact the Database and cannot be undone. Are you sure you want to delete?", "tab_name": "Tab name", "timeline": {"create_timeline": "Create Timeline", "tab": "Tab", "tab_timeline": "(Timeline) ", "new_tab": "New Tab", "card_title": "Card Title", "username": "Username", "creation_date": "Creation Date", "card_description": "Card Description", "description": "Description", "other_card_fields": "Other Fields", "image": "Image", "drag_desired_fields": "Drag the desired fields", "query_not_found": "There is no SQL configured yet.", "query_not_found__subtitle": "To view the Timeline, you need to configure an SQL first.", "buttons": "Buttons", "empty_title": "Unmapped data.", "empty_subtitle": "The SQL is configured but not linked to any registration.", "empty_subtitle_note": "Make sure to mark the ID column of the registration for everything to work correctly."}, "screen_flap": {"create_screen": "Create Screen", "change_screen": "Change screen", "change_screen_body": "To change the chosen screen, select another option in the fields below:"}, "form_flap": {"create_form": "Create Form", "create_detail": "Create Record Details", "change_form": "Change form", "change_form_body": "To change the chosen form, select another option in the field below:"}, "delete_flap": "Delete tab", "item_of": "Item of ", "edit_details_modal": "Edit Record Details", "standard_color": "Standard color", "blocking_message": "Blocking message", "delete_details_modal": "Delete Record Details", "default_button": "Default button", "delete_details_modal_title": "Delete Record Details?", "delete_details_modal_body": "Are you sure you want to delete the record details? This action cannot be undone.", "delete_details_flap_body": "Are you sure you want to delete the record details tab? This action cannot be undone.", "delete_details_button_body": "Are you sure you want to delete the record details button? This action cannot be undone.", "confirme_message": "Confirme message", "message": "Message", "accept_button_text": "\"Accept\" button text", "cancel_button_text": "\"Cancel\" button text", "configure_filters": "Configure Filters", "configure_form": "Configure Form", "enable_card_deletion": "Enable card deletion", "edit_form": "Edit form", "addition_form": "Addition form", "currency_type": "Currency type", "decimal_digits": "Decimal digits", "user_creation": "User Creation", "creation_date": "Creation date", "description": "Description", "mask": {"time": "Time", "none": "None", "telephone": "Telephone"}, "edit_filter": "Edit filter", "register": "Table", "static": "Static", "dynamic": "Dynamic", "timeline_fields": "Timeline fields", "there_no_sql_yet": "There is no SQL configured yet.", "to_view_timeline_need_sql": "To view the Timeline, you need to set up an SQL first.", "enable_mobile_version": "Enable mobile version", "web_screen": "Web Screen", "blank_card": "Blank card.", "white_card_description_1": "The card is blank because no value or button has been defined.", "white_card_description_2": "Configure the fields so that the card is populated.", "none_active_buttons": "No buttons have been added yet."}, "API": {"save_error": "Fail to save", "auth_error": "Oops, we are under maintenance. Please try again soon, we will be back shortly."}, "TABLE_ADD_MENU": {"default_title": "Create New Table", "column_headers": {"type": "Type", "default_value": "Default Value", "primary": "Primary", "unique": "Unique", "required": "Required"}, "placeholders": {"column_name": "column_name", "select_type": "Select type", "null": "NULL"}, "add_column": "Add Column", "foreign_key_section_title": "Connection with another table (Foreign key)", "relation_with": "Relation with:", "add_foreign_key_connection": "Add connection with another table", "create_table_action_fallback": "Create Table"}, "CONNECTION": {"data_source_for_ai": "Data source that will be used by AI.", "true": "true", "false": "false", "null": "Ignore", "applied_filters": "Applied Filters", "variable_list": "List of variables", "variables": "Variables", "variable": "Variable", "value": "Value", "connection": "Connection", "create_jdbc": "Create Data Source", "edit_jdbc": "Edit Data Source", "query": "Query", "empty_query": "Insert query", "run_query": "Run query", "query_error": "Fail to execute query, check if the query or JDBC are correct", "delete_connector": "Delete conector", "connector_edit": "Edit connector", "start_connection": "Create Connection", "connection_init": "How do you want to initiate?", "type_of_data": "Data type", "type_of_connection": "Connection type", "insert_connection_name": "Enter the connection name", "name": "Name", "dimension": "Table", "select_or_create_dimension": "Select or create a table", "dimension_name": "Table name", "group": "Group", "select_group": "Select or create", "insert": "Insert", "replace": "Replace", "separator": "Separator", "separator_mi": "Thousand separator", "use_this_as_calendar": "Use as calendar", "calendar_default_name": "Calendar", "select_dimension": "Select Table", "select_chip": "Select", "add_new": "Create", "rename": "<PERSON><PERSON>", "path_to_file": "File path", "no_dimension_found": "No data found", "no_cube_found": "No data found", "no_data_found": "No data", "no_data_found_description_p1_cube": "Import a CSV", "no_data_found_description_p2_cube": " or define the path to start.", "no_data_found_description_p3_sql": "Finish configuring and writing the query", "select_cube": "Select Cube", "settings_data": "Date configuration", "inferid_data": "Recognized data", "date_format": "Format", "csv_connection": "Connection", "switch_data_source": "Switch data source", "what_the_connection_type": "What will be the connection type?", "insert_configure_worksheet": "Insert and configure the worksheet", "check_fields": "Check the fields", "insert_worksheet_configure_path_view_data": "Insert the worksheet or configure the path to view the data.", "insert_query_configure_path_view_data": "Insert the query to view the data.", "insert_query_configure_timeline": "Insert the query and associate the columns to configure the timeline", "check_fields_body": "To conclude, it is necessary that all the fields below are defined in the worksheet ✅. It is important that the order is also the same.", "configure_JDBC": "Configure the JDBC", "configure_and_run_query": "Configure and run the query", "execute": "Execute", "inactive": "Inactive", "loading": "Loading", "preview": "Preview", "resume": "Resume", "templates": "Templates", "quick_consultation": "Quick consultation ", "quick_consultation_body": "Do your search quickly using one of the templates below:", "change": "Change", "define_search_arguments": "Define the search arguments:", "select_all": "Select all", "start_with_template": "Start with Template", "remove": "Remove", "new_dimension": "New table", "schedule": "Scheduling (Cron)", "schedule_invalid": "<PERSON><PERSON>uling (Cron) invalid", "generator": "Generator", "connection_log": "Connection log", "run_by": "Run by", "date_and_time": "Date and time", "summary": "Summary", "success": "Success | Sucesses", "partial_success": "Partial success | Partial successes", "failure": "Failure | Failures", "failed": "Failed", "search": "Search...", "connection_not_have_logs": "This connection does not currently have any log records.", "log_summary": "Log summary", "detailing": "Detailing", "cleaning_the_cubes": "Cleaning the cubes", "structure": "Structure", "accepted_lines": "Accepted Lines", "rejected_lines": "Rejected Lines", "reason_for_rejections": "Reason for rejections", "uploader_type": ".CSV here", "description": "Description", "enter_the_connection_name": "Enter the connection name", "cron_expression_generator": "Cron expression generator", "active": "Active", "enlarged_view": "Expanded view", "connection_name": "Connection name", "port": "Port", "database": "Database", "user": "User", "password": "Password", "field_required": "Required field", "authentication_failure": "Incorrect username or password.", "user_does_not_have_access": "This user does not have permission to access this JDBC.", "no_results_found": "No results found", "delete_connection_body_1": "Are you sure you want to delete the connection?", "delete_connection_body_2": "This action cannot be undone.", "this_screen_is_preview_mode": "This screen is in preview mode", "connection_dimension_deleted": "The table linked to this connection has been deleted!", "last_run_of_connection_failed": "The last run of this connection failed!", "when_editing_data_source_review_fild_formats": "When editing the data source, review the field formats.", "check_faults_here": "Check faults here", "delete_connection": "Delete connection", "delete_connection_body": "This action cannot be undone. Are you sure you want to delete?", "break_bond": "Break bond", "execution_time": "Execution time", "something_went_wrong": "Something went wrong", "connection_could_not_established": "The connection could not be established because the registration", "requires_a_field": "requires a field", "which_not_provided_when_uploading": "which was not provided when uploading the spreadsheet.", "please_check_the_spreadsheet": "Please check the spreadsheet and ensure you include the required field", "before_trying_again": "before trying again", "failed_execute_query": "Data execution failed", "unexpected_error_execute_query": "An unexpected problem occurred while executing the data for this connection.", "error_detail": "Error details:", "confirmation": "Confirmation", "here_below_structures_review": "Check out the structures that will be created or populated with this connection:", "connection_registration_of": "Attributes of ", "origin": "Origin", "id_type": "ID Type", "type": "Type", "new": "New!", "native": "Native", "existent": "Existent", "change_key": "Change key", "affected_members_will_be_new": "All affected members will be new", "change_existing_members": "I will make changes to existing members using the member's 'Description' as the key", "description_of": "Description of ", "id_of": "ID of ", "select_connection_type": "Select connection type", "data_source": "Data source", "connections": "Connections", "switch_to_sql": "Switch to SQL", "switch_to_csv": "Switch to CSV", "import_from_external_database": "Import from external DB", "scheduling": "Scheduling", "without_scheduling": "Without scheduling", "run_now": "Run now", "executed_there": "Executed", "executing": "In progress", "execution_failures": "Execution Failures", "failed_execute": "Execution failed", "columns": "Columns", "new_columns": "Type", "setting": "Setting", "numeric": "Numeric", "text": "Text", "date": "Date", "descr_other_column": "Description of another column", "descr_of_fk": "Description of an FK", "should_become_description": "should become description of", "choose_column": "Choose column", "day": "Day", "view_log": "View log", "table": "Table", "cube": "C<PERSON>", "new_column": "New column", "existing_column": "Existing column", "new_connection": "New Connection", "connection_type": "Type of Connection", "connection_2": "Connection", "connection_name_2": "Connection name", "import_from_eip": "Import from EIP", "create_empty_table": "Create empty table", "you_havent_uploaded_yet": "You haven't uploaded yet", "will_all_rows_be_new": "Will all rows be new in the table?", "yes_all_lines_will_be_new": "Yes, all lines will be new.", "no_use_id_to_update_existing": "No, use the ID to update existing records.", "no_use_description_to_update_existing": "No, use the description to update existing records.", "using_fks_as_replacement_key": "Using one of the FKs as a replacement key", "clean_cubes_before_loading": "Clean cubes before loading", "clear_files_day_range": "Clear the file's day range before loading", "clear_query_day_range": "Clear the query's range of days before loading", "save_editions": "Save editions", "save_and_run": "Save and Run", "total_values": "Total values", "data_source_name": "Data source name"}, "SHEET": {"DYNAMIZATION": {"title": "Dynamization", "edit_dynamization": "Edit Dynamization", "create_dynamization": "Create Dynamization"}, "CONTEXT_MENU": {"cut_out": "Cut out", "copy": "Copy", "paste": "Paste", "insert_row_below": "Insert 1 row below | Insert {count} rows above", "insert_row_above": "Insert 1 row above | Insert {count} rows above", "insert_column_left": "Insert 1 column to the left | Insert {count} columns to the left", "insert_column_right": "Insert 1 column to the right | Insert {count} columns to the right", "insert_cells": "Insert cells", "unfreeze_rows": "Unfreeze lines", "unfreeze_columns": "Unfreeze columns", "insert_cells_and_shift_right": "Insert cells and shift right", "insert_cells_and_shift_down": "Insert cells and shift down", "delete_cells_and_shift_left": "Delete cells and shift left", "delete_cells_and_shift_up": "Delete cells and shift up", "delete_selected_lines": "Delete selected lines", "delete_selected_columns": "Delete selected columns", "clear_selected_columns": "Clear selected columns", "clear_selected_lines": "Clear selected lines", "insert_copied_cut": "Insert copied or cut", "delete_line": "Delete line | Delete lines", "delete_column": "Delete column | Delete columns", "delete_cells": "Delete cells", "transform_to_dynamic_cell": "Transform to dynamic cell", "see_more_cell_actions": "See more cell actions", "conditional_formatting": "Conditional formatting", "to_the_left": "to the left", "to_the_right": "to the right", "clear_column": "Clear column | Clear columns", "hide_column": "Hide column | Hide columns", "display_column": "Display column", "sort_A_to_Z": "Sort from A to Z", "sort_Z_to_A": "Sort from Z to A", "see_more_column_actions": "See more column actions", "group_column": "Group columns", "clear_line": "Clear line | Clear lines", "hide_line": "Hide line | Hide lines", "show_row": "Show row", "see_more_line_actions": "See more line actions", "freeze_up_to_column": "Freeze up to column", "freeze_up_to_row": "Freeze up to row", "group_line": "Group lines", "delete": "Delete", "hide": "<PERSON>de", "move_right": "Move right", "move_left": "Move left"}, "VIEW": {"edit_data": "edit data", "show_all": "Show all", "block_as_line": "Block as line", "filters": "Filters", "no_created_filter": "No filter was created", "add_lines": "Add in line", "select_below": "Select a data below", "add_column": "Add in column", "disabled_range": "We have disabled the automatic range to apply the formula", "automatic_range": "Automatic range", "add_in_data": "Add in data", "data_not_be_metric": "This data type cannot be added as a metric", "data_only_be_metric": "Data type {type} can only be added as a metric"}, "RANGE": {"area_range": "Sheet range", "select_range": "Select a range", "invalid_range": "Invalid Range"}, "FORMULAS": {"sum": "Sum", "average": "Average", "count_numbers": "Count numbers", "maximum": "Maximum", "minimum": "Minimum"}, "FORMATTER": {"undo": "Undo (Ctrl + Z)", "redo": "Redo (Ctrl + Y)", "format_as_currency": "Format as currency", "format_as_percentage": "Format as percentage", "decrease_number_decimal_places": "Decrease number of decimal places", "increase_number_decimal_places": "Increase number of decimal places", "more_formats": "More formats", "simple_text": "Simple text", "formatting": "Formatting", "font_size": "Font size", "bold": "Bold", "italic": "Italic", "text_color": "Text color", "fill_color": "Fill color", "wrapping": {"title": "Text fit", "wrap": "Wrap", "overflow": "Overflow", "break": "Break"}, "edges": "<PERSON>s", "edge_style": "Edge style", "edge_type": {"all": "All borders", "inside": "Inside borders", "horizontal": "Horizontal borders", "outside": "Outside borders", "vertical": "Vertical borders", "top": "Top border", "bottom": "Bottom border", "left": "Left border", "right": "Right border", "none": "No border"}, "color": "Color", "merge_cells": "Merge cells", "align_horizontally": "Align horizontally", "align_vertically": "Align vertically", "filter": "Filter", "functions": "Functions", "automatic": "Automatic", "number": "Number", "percentage": "Percentage", "coin": "Coin", "accounting": "Accounting", "merge_all": "<PERSON>rge <PERSON>", "merge_vertically": "<PERSON><PERSON>", "merge_horizontally": "<PERSON><PERSON>", "unmerge": "Unmerge", "left": "Left", "center": "Center", "right": "Right", "top": "Top", "middle": "Middle", "bottom": "Bottom"}, "sheet_search": "Search...", "set_pivot_range": "Set the pivot range", "select_all": "Select all", "clear": "Clear", "bars_lines_area": "Bar/Lines/Area", "graphics": "Graphics", "dynamization_area": "Dynamization area", "systematize": "Systematize", "column": "column", "columns": "columns", "row": "row", "rows": "rows", "x_axis": "X axis", "dimension": "Table", "format": "Format", "select_format": "Select a format", "search": "Search", "new_metric": "New Metric", "add_cube": "Add cube", "table_range": "Table range", "add_registration": "Add table", "please_select_valid_range": "Please select a valid range for", "view_columns": "view the columns", "use_description_key": "Use description as key", "column_2": "Column", "columns_2": "Columns", "mapping": "Mapping", "select_data_below": "Select a data below", "configure_view": "Configure View", "graphics_editor": "Graphics Editor", "chart_type": "Chart type", "elements": "Elements", "axle": "<PERSON><PERSON><PERSON>", "series_generator_axis": "Series generator axis", "metrics": "Metrics", "row_2": "Row", "rows_2": "Rows", "personalization": "Personalization", "title_text": "Title text", "style": "Style", "chart_title": "Chart title", "none_selected": "None selected", "add_filter": "Add filter", "select_all_2": "(Select all)", "edit_metric": "Edit Metric", "formula": "Formula", "axis_x": "Axis X", "available_formulas": "Available Formulas", "allow_data_entry": "Allow data entry", "complete": "Complete", "add_data": "Add data", "empty_block": "Empty Block", "create_new": "Create new", "native": "Native", "protected_cell_warning": "Unable to change the value of a protected cell"}, "TOAST": {"fail_id_length_exceeded": "Your ID exceeds the 64 character limit", "fail_edit_workspace_color": "Falha ao editar cor do workspace", "duplicate_screen_error": "Error duplicating screen", "error_project_load": "Error loading available projects", "fail_rename_sheet": "Fail on rename spreadsheet", "fail_delete_sheet": "Fail on delete spreadsheet", "define_destination_emails": "Define destination email", "members_not_found": "Project member not found", "fail_remove_member": "Failed to remove member.", "fail_to_rename_worksheet": "Failed to rename worksheet", "fail_to_rename_screen": "Failed to rename screen", "file_not_found": "File not found", "fail_to_upload_file": "Failed to upload the file!", "select_module": "Select Module", "add_one_dimension_dataset": "Add at least one table to the Dataset", "unable_add_same_dimension": "Unable to add the same table", "email_code_was_not_confirmed": "Email code was not confirmed", "failed_create_folder": "Failed to create new folder", "fail_to_rename_folder": "Failed to rename folder", "fail_rename_workspace": "Failed to rename workspace", "fail_delete_workspace": "Failed to delete workspace", "user_already_registered": "User is already registered!", "fail_change_password": "Failed to change password", "fail_change_name": "Failed to change name.", "fail_change_profile_picture": "Failed to change profile picture.", "name_changed_successfully": "Name changed successfully.", "password_changed_successfully": "Password changed successfully.", "profile_photo_successfully_changed": "Profile photo has been successfully changed.", "duplicated_name": "Duplicated name.", "error_create_data_loader": "Failed to create import", "error_create_online_table": "Falha ao criar tabela online", "cube_name_already_exists": "Cube name already exists.", "folder_name_already_exists": "Failed to add folder, the name already exists.", "screen_name_already_used": "Screen name already used in this folder.", "failed_load_screens": "Failed to load screens!", "failed_load_content_dimension": "Failed to load members of this table!", "failed_invite_member": "Failed to invite new member", "merge_credentials_not_found": "User without credentials to access this workspace!", "unable_delete_field": "Unable to delete field!", "failed_create_flap": "Failed to create tab!", "dimensions_belong_to_same_branch": "Tables belong to the same branch.", "error_choosing_field": "Error choosing field!", "error_running_query": "Falha ao executar a query", "error_assistant_id_invalid_key": "Failed to execute query", "synchronize_table_error": "Failed to synchronize table", "error_connection_failed": "Connection failed", "error_jdbc_offline": "Select an online JDBC connection", "error_saving_data_loader": "Error editing import", "error_loading_data_loader_log": "Error loading import log", "error_assistant_id_no_model": "You do not have enough credits to access the OpenAI GPT-4 API. Add credits through the OpenAI payment panel.", "merge": "Merge."}, "SCREENS": {"share_dashboard": "Share Dashboard"}}