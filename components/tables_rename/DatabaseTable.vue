<template>
	<div
		v-if="!freeDbOnlineTableError"
		class="relative flex w-full flex-col overflow-hidden bg-white"
		:class="{ 'p-2': !loadingTableContent }"
	>
		<div
			v-if="props.displayName"
			class="mx-2 mb-5 ml-3 flex items-center justify-between font-bold"
		>
			{{ props.dimension.name }}
			<svg-icon
				class="cursor-pointer"
				color="grey"
				type="mdi"
				size="18"
				:path="mdiClose"
				@click="emit('update-view', $event)"
			></svg-icon>
		</div>

		<div
			v-if="loadingTableContent"
			class="absolute z-20 h-full w-full bg-grey-300"
		>
			<div class="h-full w-full animate-pulse bg-grey-200"></div>
		</div>
		<div
			v-if="isInvalidTable"
			class="absolute z-20 h-full w-full bg-white"
		>
			<div class="h-full w-full items-center justify-center flex">
				<div class="flex items-center flex-col gap-2">
					<div
						class="rounded-lg h-[52px] w-[52px] flex items-center justify-center"
						:class="{
							'bg-[--sankhya-green-50]': useWhiteLabel().isSankhyaClient,
							'bg-[--violet-100]': !useWhiteLabel().isSankhyaClient
						}"
					>
						<img
							src="~/assets/images/mockups/close_eye.svg?componentText"
							alt="csvIcon 'w-[20px] h-[20px]"
						/>
					</div>
					<div class="text-xl text-grey-900 font-medium pt-4">
						{{ $t('DATABASE.preview_not_available') }}
					</div>
					<div class="text-base text-grey-600 font-normal max-w-[513px] text-center">
						{{ $t('DATABASE.not_configured') }}
					</div>
				</div>
			</div>
		</div>

		<component
			:is="TableComponent"
			ref="supersheet"
			:table-id="dimension.id"
			:items="list"
			:col-widths="colWidths"
			:nested-headers="nestedHeaders"
			:add-line="enableAddRow && !props.dimension.isFreeDbOnlineTable"
			:add-column="enableAddCol"
			:data-key-name="{
				isList: true,
				contentViews: '',
				contentType: 'dimensionAttributeType'
			}"
			:fixed="1"
			:row-count="rowCount"
			:row-height="32"
			:native="dimension.isNative"
			:free-db-online-table="props.dimension.isFreeDbOnlineTable"
			:on-board="onBoard"
			:order-list="orderList"
			:enable-data-entry="enableDataEntry"
			:online-tags="onlineTags"
			is-free-db
			@add-row="addRow"
			@data-entry="applyEntry"
			@context-menu="setRowMenu"
			@context-header="openAttributeMenu($event.item, $event.idx)"
			@load-data="loadData"
			@width-list="updateWidth"
		>
			<template
				#header="{ item, col }: { item: HeaderContent | ViewHeaderItem, col: number }"
			>
				<div
					class="width-fill-available header root mx-[8px] flex items-center"
					:class="{
						'cursor-pointer':
							item.dimensionAttributeType === 'FK' ||
							(item.dimensionAttributeType.includes('Link') &&
								(!merge || !item.dimensionAttributeType.includes('User'))),
						visualAutomationType: item?.visualAutomationType,
						fkHeader: item.dimensionAttributeType === 'MultiLinkDimension'
					}"
					@click="openDimensionTab(item)"
				>
					<div class="mr-[6px] flex items-center justify-center">
						<svg-icon
							v-if="item.dimensionAttributeType !== 'MultiLinkDimension'"
							height="15px"
							color="var(--grey-600)"
							type="mdi"
							:path="icons[item.dimensionAttributeType]"
						></svg-icon>
						<img
							v-else
							src="~~/assets/icons/to_replace/workflow.svg?componentText"
							width="16"
							height="16"
							class="mr-[3px]"
						/>
					</div>
					<div
						:title="item.name"
						class="truncate"
					>
						{{ item.name }}
					</div>
					<div
						v-if="!item?.inPrimaryKey && !props.dimension.isFreeDbOnlineTable"
						class="ml-auto flex cursor-pointer flex-col justify-center p-[6px]"
						@click.stop.prevent="openAttributeMenu(item, col)"
						@mousedown.stop
					>
						<a-tooltip
							v-if="merge && col > 1 && item.dimensionAttributeType === 'MultiLinkDimension'"
							placement="bottom"
							overlay-class-name="base-tooltip"
						>
							<template #title>
								<span class="base-tooltip-text">{{ $t('DATABASE.not_column') }}</span>
							</template>

							<svg-icon
								size="18"
								type="mdi"
								:path="mdiInformationOutline"
								color="rgba(46, 144, 250, 1)"
								class="content-icon"
							></svg-icon>
						</a-tooltip>
						<img
							v-else
							src="~~/assets/icons/arrow/arrow_down.svg?componentText"
							width="6"
							height="6"
						/>
					</div>
				</div>
				<a-popover
					v-if="selectedHeaderIdx === col"
					v-model:visible="attributesMenuController"
					:overlay-class-name="'w-[130px]'"
					:placement="
						col < 3 ? (onBoard ? 'right' : 'bottomLeft') : onBoard ? 'left' : 'bottomRight'
					"
					trigger="click"
					arrow-point-at-center
				>
					<template #content>
						<TableAttributeContextMenu
							:options="[
								{
									title: $t('GLOBAL.edit'),
									icon: mdiPencil,
									action: 'toggleEditAttributeModal',
									disabled: false
								},
								{
									title: $t('GLOBAL.delete'),
									icon: mdiDeleteOutline,
									action: 'openDeleteAttributeModal',
									disabled: false
								}
							]"
							@selected="executeSelectedOption"
						/>
					</template>
				</a-popover>
			</template>
			<template #default="{ item, col, row }">
				<TableDynamicCell
					v-if="
						!useEnableDataEntryTypes()[nestedHeaders[col].dimensionAttributeType ?? '']
					"
					display-picture
					:class="{
						fkCell: nestedHeaders?.[col].dimensionAttributeType === 'MultiLinkDimension'
					}"
					:content="item"
					:header="nestedHeaders[col]"
					:is-free-db-online-table="props.dimension.isFreeDbOnlineTable"
					:data-source-id="Number(dimension.dataSourceId)"
					:saving="linkRecordSaving && list[row].internalId"
					@set-data-entry="setValueByDynamicCell($event, row, col)"
				/>
				<span
					v-else
					class="px-2"
					:class="{
						fkCell: nestedHeaders?.[col].dimensionAttributeType === 'MultiLinkDimension'
					}"
					>{{
						item !== null &&
						item !== '#Error' &&
						nestedHeaders[col].dimensionAttributeType === 'Number'
							? Number(item).toFixed(nestedHeaders[col].precision || 2)
							: item
					}}</span
				>
				<div
					v-if="openRowMenu && `${row}-${col}` === activePosition"
					class="flex items-end font-sans hover:cursor-pointer"
				>
					<a-popover
						v-model:visible="openRowMenu"
						overlay-class-name="w-[100px]"
						trigger="click"
						placement="bottomLeft"
					>
						<template #content>
							<div
								class="cursor-pointer hover:bg-[#eaecf0]"
								@click="deleteRow(row)"
							>
								{{ $t('GLOBAL.delete') }}
							</div>
						</template>
					</a-popover>
				</div>
			</template>
			<template #footer>
				<div
					class="flex items-center justify-center pl-[10px] hover:cursor-pointer"
					data-cy="btn-add-row"
					title="$t('DATABASE.add_row')"
				>
					<svg-icon
						size="16"
						color="#313854"
						type="mdi"
						:path="mdiPlus"
					></svg-icon>
				</div>
			</template>
			<template #addcol>
				<div
					class="root flex min-w-[80px] max-w-[80px] items-center justify-center font-sans hover:cursor-pointer"
					data-cy="btn-add-column"
					:title="$t('DATABASE.add_column')"
					@click="addColumnMenu()"
				>
					<svg-icon
						size="16"
						color="#313854"
						type="mdi"
						:path="mdiPlus"
					></svg-icon>
				</div>
			</template>
		</component>

		<BaseNoticeModal
			v-model:modalController="confirmModalController"
			:main-icon="mdiAlertCircleOutline"
			default-buttons
			:main-text="`${$t('DATABASE.sure_to_delete_attribute')} ${
				selectedAttribute.name
			} ?`"
			:sub-text="$t('DATABASE.action_cannot_undone')"
			@cancel="confirmModalController = false"
			@confirm="deleteAttribute"
		>
		</BaseNoticeModal>
	</div>
	<DatabaseEmptyState
		v-else
		:free-db-online-table-error="freeDbOnlineTableError"
	/>
</template>

<script setup lang="ts">
	import {
		mdiClose,
		mdiDeleteOutline,
		mdiPencil,
		mdiPlus,
		mdiAlertCircleOutline,
		mdiInformationOutline
	} from '@mdi/js';
	import SmartSheet from './Sheet/SmartSheet.vue';
	import DatabaseSmartSheet from './Sheet/DatabaseSmartSheet.vue';
	import toastNotify from '~~/helpers/toast_notify';
	import { TABLE } from '~~/helpers/contants/table_constants';

	const emit = defineEmits(['update-view', 'open-tab']);

	const props = defineProps({
		onBoard: {
			type: Boolean,
			default: false
		},
		loadOnCreated: {
			type: Boolean,
			default: false
		},
		dimension: {
			type: Object as PropType<Dimension>,
			required: true
		},
		displayName: {
			type: Boolean,
			default: true
		},
		fixed: {
			type: Number,
			default: 0
		},
		search: {
			type: String,
			default: ''
		},
		isDynamic: {
			type: Boolean,
			required: false,
			default: false
		}
	});

	watch(
		() => props.search,
		() => {
			loadDimensionContent({ displayLoadingFrame: true });
		}
	);
	// const duplicateID = useNuxtApp().$translate('DATABASE.duplicate_id');
	const TableComponent = props.fixed ? DatabaseSmartSheet : SmartSheet;
	const icons = useDataTypeIcons();
	const { merge } = useAuthStore();
	const { enableAddRow, linkRecordSaving } = useTableUI(props);
	const freeDbService = useFreeDbService();
	const metadataStore = useMetadataStore();
	// const { activeTableReaderId } = storeToRefs(metadataStore);
	const enableAddCol = ref(true);
	const headers = ref<HeaderContent[]>([]);
	const colWidths = ref<number[]>([]);
	const orderList = ref<number[]>([]);
	const list = ref<Content[]>([]);
	// const whiteLabelPrimaryTextColor = computed(() => {
	// 	return useWhiteLabel().isSankhyaClient
	// 		? 'var(--grey-500)'
	// 		: 'var(--violet-600)';
	// });
	// const isOnlineTable = computed(
	// 	() => activeTableReaderId?.value || props.isDynamic
	// );
	const supersheet = ref<InstanceType<typeof SmartSheet> | null>(null);
	const enableDataEntry = ref(false);
	const attributesMenuController = ref(false);
	const editAttributeController = ref(false);
	const selectedHeaderIdx = ref(0);
	const rowCount = ref(0);
	const openRowMenu = ref(false);
	const loadingTableContent = ref(false);
	const activePosition = ref('-');
	// const enableOptions = ref(false);
	const createAttibuteController = ref(true);
	const confirmModalController = ref(false);
	const confirmModalExecutionLoading = ref(false);
	const isInvalidTable = ref(false);
	const freeDbOnlineTableError = ref(false);
	const onlineTags = ref<
		{
			id: number | null;
			name: string;
			color: string;
			createdAt: Date | null;
			createdBy: string | null;
			updatedAt: Date | null;
			updatedBy: string | null;
		}[]
	>([]);

	const selectedAttribute = ref<HeaderContent>({
		id: 0,
		name: '',
		isNative: false,
		dimensionAttributeType: 'SingleLineText',
		options: []
	});

	props.loadOnCreated && loadDimensionContent({ displayLoadingFrame: true });

	useEventListener(document, 'keydown', (event) => {
		if (
			(event.metaKey || event.ctrlKey) &&
			document.activeElement?.tagName !== 'INPUT'
		) {
			if (event.key === 'x') {
				copyData();
				supersheet.value?.deleteContent();
			}
			if (event.key === 'c') {
				copyData();
			}
			if (event.key === 'v') {
				pasteData();
			}
		}
	});

	const dialogStore = useDialogStore();
	const { dynamicCubeModalController, editDynamicCubeModalController } =
		storeToRefs(dialogStore);

	watch(
		() => attributesMenuController.value,
		() => {
			if (dynamicCubeModalController.value || editDynamicCubeModalController.value) {
				if (editDynamicCubeModalController.value) {
					editAttributeController.value = true;
				}
				attributesMenuController.value = true;
			}
		}
	);

	const nestedHeaders = computed(() => {
		return headers.value;
	});

	async function addRow() {
		const content = headers.value.map((h) =>
			(h as any)?.columnType === 'BOOLEAN'
				? !!(h as any)?.columnDefault
				: (h as any)?.columnType === 'DATE'
				? null
				: (h as any)?.columnDefault ?? null
		);
		list.value.push(content);
		let col: null | number = null;
		let isAutoincrement = false;
		const inPrimaryKey: string[] = [];
		const index = headers.value.findIndex((header, index) => {
			if (header.inPrimaryKey) {
				inPrimaryKey.push(header.name);
			}
			if (header.inPrimaryKey && header.isAutoIncrement) {
				isAutoincrement = true;
			}
			if (col === null && !header.inPrimaryKey) col = index;
			return header.isRequired;
		});
		if (isAutoincrement && index === -1) {
			const payload: { contents: any[] } = {
				contents: [{ primaryKeyValues: {}, values: {} }]
			};
			inPrimaryKey.forEach((key) => {
				payload.contents[0].primaryKeyValues[key] = 'null';
			});
			const { data } = await freeDbService.addRowTable(
				`${props.dimension.id}`,
				Number(props.dimension.dataSourceId),
				payload
			);
			if (data) {
				headers.value.forEach((header, index) => {
					if (header.inPrimaryKey) {
						list.value[list.value.length - 1][index] = data.contents[0].values[header.name];
					}
				});
			}
		}
		if (index !== -1) col = index;
		if (col || col === 0)
			setTimeout(() =>
				supersheet.value?.focusCell(list.value.length - 1, Number(col))
			);
		rowCount.value += 1;
	}

	function setValueByDynamicCell(event: any, row: number, col: number) {
		const value = event;
		applyEntry({ [`${row}-${col}`]: value });
	}

	async function applyEntry(dataEntry: DataEntry) {
		const metadata: { [key: number]: { index: number; valid: boolean } } = {};
		const preePayload: { contents: any[] } = { contents: [] };
		const payload: { contents: any[] } = { contents: [] };
		const addPayload: { contents: any[] } = { contents: [] };
		for (const key in dataEntry) {
			const position = key.split('-');
			if (position.length === 2) {
				const row = Number(position[0]);
				const col = Number(position[1]);

				const value = dataEntry[key];

				if (!metadata[row]) {
					metadata[row] = {
						index: preePayload.contents.length,
						valid: true
					};
					const data: {
						primaryKeyValues: { [key: string]: any };
						values: { [key: string]: any };
					} = {
						primaryKeyValues: {},
						values: {}
					};
					headers.value.forEach((header, index) => {
						const content = list.value?.[row]?.[index];
						if (header.inPrimaryKey) {
							data.primaryKeyValues[header.name] = content;
							if (content === null) metadata[row].valid = false;
						}
						if (header.isRequired && content === null) {
							metadata[row].valid = false;
						}
					});
					preePayload.contents.push(data);
				}
				const content =
					(headers.value[col] as any)?.dimensionAttributeType === 'FK'
						? Array.isArray(value)
							? value?.[0]?.id || null
							: (value as any)?.id || value
						: value;
				if (headers.value[col].inPrimaryKey) {
					preePayload.contents[metadata[row].index].primaryKeyValues[
						headers.value[col].name
					] = content;
				} else {
					preePayload.contents[metadata[row].index].values[headers.value[col].name] =
						content;
				}

				list.value[row][col] = useCloneDeep(value);
			}
		}

		for (const [key, value] of Object.entries(metadata)) {
			if (value.valid) {
				payload.contents.push(preePayload.contents[value.index]);
			} else {
				let isValid = true;
				const content: { values: { [key: string]: any } } = { values: {} };
				for (const [index, header] of headers.value.entries()) {
					const data = list.value[Number(key)][index];
					const value =
						(headers.value[index] as any)?.dimensionAttributeType === 'FK'
							? Array.isArray(data)
								? data?.[0]?.id || null
								: (data as any)?.id || data
							: data;
					if (value !== null) content.values[header.name] = value;
					if (header.isRequired && value === null) {
						isValid = false;
					}
				}
				if (isValid) addPayload.contents.push(content);
			}
		}

		if (payload.contents.length !== 0) {
			try {
				const { error } = await freeDbService.dataEntryTable(
					`${props.dimension.id}`,
					Number(props.dimension.dataSourceId),
					payload
				);
				if (error) {
					toastNotify(error?.message || 'update error');
				}
			} catch (error) {
				toastNotify((error as any)?.message || 'update error');
			}
		}

		if (addPayload.contents.length !== 0) {
			try {
				const { data, error } = await freeDbService.addRowTable(
					`${props.dimension.id}`,
					Number(props.dimension.dataSourceId),
					addPayload
				);
				let fixIndex = 0;
				for (const [key, value] of Object.entries(metadata)) {
					if (value.valid) fixIndex++;
					else
						headers.value.forEach((header, col) => {
							if (header.inPrimaryKey) {
								list.value[Number(key)][col] =
									data.contents[value.index - fixIndex].values[header.name];
							}
						});
				}
				if (error) {
					toastNotify(error?.message || 'create error');
				}
			} catch (error) {
				toastNotify((error as any)?.message || 'create error');
			}
		}
	}

	function openDimensionTab(item: HeaderContent | ViewHeaderItem) {
		if (item.dimensionAttributeType === 'FK') {
			metadataStore.addOnMetadataList({
				name: item?.referencedTable,
				id: item?.referencedTable,
				dataSourceId: Number(props.dimension.dataSourceId),
				isFreeDb: true
			});
		} else if (
			item.dimensionAttributeType.includes('Link') &&
			(!merge || !item.dimensionAttributeType.includes('User'))
		)
			if (props.onBoard) emit('open-tab', item);
			else
				metadataStore.addOnMetadataList({
					name: item?.mirrorDimensionId
						? item.name.split(`(${props.dimension.name})`)?.[0]
						: item.name,
					id:
						item?.mirrorDimensionId ??
						(item.id as number & { id: number }).id ??
						item.referencedDimensionId,
					isDimension: true,
					isNative: item.isNative,
					mirrorDimensionId: item.mirrorDimensionId
				} as Dimension);
	}

	function toggleEditAttributeModal() {
		attributesMenuController.value = false;
		const header = headers.value[selectedHeaderIdx.value];
		metadataStore.setColToEdit(header);
		useDialogStore().switchDialog('databaseColumnController', true);
	}

	function toggleAttributePopup(value: boolean) {
		attributesMenuController.value = value;
	}

	function openAttributeMenu(item: any, idx: number) {
		if (!item.isNative) {
			selectedHeaderIdx.value = idx;
			// if (!item.id) item.name = '';
			selectedAttribute.value = item;
			createAttibuteController.value = !!item.id;
			toggleAttributePopup(true);
		}
	}

	async function deleteRow(row: number) {
		const id = list.value[row][0];
		const payload: { contents: any[] } = {
			contents: [
				{
					primaryKeyValues: {}
				}
			]
		};
		headers.value.forEach((header, index) => {
			if ((header as any)?.inPrimaryKey) {
				payload.contents[0].primaryKeyValues[header.name] = list.value[row][index];
			}
		});
		const removeLine = () => {
			list.value.splice(row, 1);
			rowCount.value = list.value.length;
		};
		openRowMenu.value = false;
		if (id) {
			const { error } = await freeDbService.deleteTableLine(
				`${props.dimension.id}`,
				Number(props.dimension.dataSourceId),
				payload
			);
			if (!error) removeLine();
			else toastNotify(error?.message || 'delete error');
		} else removeLine();
	}

	function setRowMenu(key: string) {
		if (!props.dimension.isNative) {
			activePosition.value = key;
			openRowMenu.value = true;
		}
	}

	async function loadData() {
		const { data } = await freeDbService.getTableData(
			`${props.dimension.id}`,
			Number(props.dimension.dataSourceId),
			list.value.length
		);
		if (data) {
			list.value = [...list.value, ...data.content];
		}
	}

	async function loadDimensionContent({
		displayLoadingFrame
	}: {
		displayLoadingFrame?: boolean;
		dimensionId?: number;
	}) {
		if (displayLoadingFrame) loadingTableContent.value = true;

		if (props.dimension.isFreeDbOnlineTable) {
			loadDimensionContentOfFreeDbOnlineTable();
			return;
		}
		const { data } = await freeDbService.getTableData(
			`${props.dimension.id}`,
			Number(props.dimension.dataSourceId)
		);
		if (data?.tableType === 'TABLE') enableDataEntry.value = true;
		headers.value = data.headers?.map((header: any) => {
			return {
				...header,
				dimensionAttributeType: header.referencedTable ? 'FK' : header?.columnType
			};
		});
		colWidths.value = getWidthList(headers.value);
		list.value = data.content;
		rowCount.value = data.totRows;
		loadingTableContent.value = false;
	}

	// Load FreeDb
	async function loadDimensionContentOfFreeDbOnlineTable() {
		try {
			const { freeDbOnlineTableList } =
				await useFreeDbService().getFreeDbOnlineTables();
			if (freeDbOnlineTableList) {
				// procuro o de mesmo ID
				const tableRef = freeDbOnlineTableList.find(
					(item: any) => item.id === props.dimension.id
				);
				if (tableRef) {
					// Agora eu carrego os valores do preview dele.
					const { data } = await useLegacyService().runViewQueryPreview({
						jdbcConnectionConfigId: tableRef.jdbcConnectionConfigId,
						useOnlineTables: false,
						query: tableRef.query
					});
					onlineTags.value = tableRef.tags;

					enableDataEntry.value = false;
					// enableAddRow.value = false;
					enableAddCol.value = false;

					headers.value = data.headers?.map((header: any) => {
						return {
							...header,
							dimensionAttributeType: adjustQueryPreviewType(header.dataType)
						};
					});
					colWidths.value = getWidthList(headers.value);
					list.value = convertDateFormatQueryPreview(
						headers.value,
						data.queryResult.slice(1)
					);
					rowCount.value = data.queryResult.slice(1).length;
					loadingTableContent.value = false;
					updateTableForIaRef(tableRef);
				}
			} else {
				throw new Error('No table found');
			}
		} catch (error) {
			freeDbOnlineTableError.value = true;
		}
	}

	function updateTableForIaRef(tableRef: FreeDbOnlineTableForm) {
		useFreeDbService().updateFreeDbOnlineTable({
			id: tableRef.id,
			name: tableRef.name,
			query: tableRef.query,
			jdbcConnectionConfigId: tableRef.jdbcConnectionConfigId,
			tags: tableRef.tags
		});
	}
	function convertDateFormatQueryPreview(headers: any, queryResult: any): any[] {
		if (!Array.isArray(queryResult)) return [];

		// quais colunas são DATE?
		const dateColIdxs: number[] = (headers || []).reduce(
			(acc: number[], h: any, i: number) => {
				const type = (h?.dimensionAttributeType || '').toString().toUpperCase();
				if (type === 'DATE') acc.push(i);
				return acc;
			},
			[]
		);

		if (dateColIdxs.length === 0) return queryResult;

		// converte as células de data nas colunas encontradas
		return queryResult.map((row: any) => {
			if (!Array.isArray(row)) return row;
			const newRow = [...row];

			for (const idx of dateColIdxs) {
				const raw = newRow[idx];
				if (typeof raw === 'string' && raw.trim()) {
					newRow[idx] = toIsoLike(raw);
				}
			}
			return newRow;
		});
	}

	// Helpers
	function toIsoLike(input: string): string {
		// já está em ISO-like? (YYYY-MM-DD [T| ]HH:mm[:ss])
		const isoLike = /^\d{4}-\d{2}-\d{2}(?:[ T]\d{2}:\d{2}(?::\d{2})?)?$/;
		if (isoLike.test(input))
			return input.replace('T', ' ').replace(/(\d{4}-\d{2}-\d{2})$/, '$1 00:00:00');

		// DD/MM/YYYY [HH:mm[:ss]]
		const m = input.match(
			/^(\d{1,2})\/(\d{1,2})\/(\d{4})(?:[ T](\d{1,2}):(\d{1,2})(?::(\d{1,2}))?)?$/
		);
		if (!m) return input; // não bateu o padrão? não altera.

		const dd = m[1].padStart(2, '0');
		const mm = m[2].padStart(2, '0');
		const yyyy = m[3];
		const HH = (m[4] ?? '00').padStart(2, '0');
		const MI = (m[5] ?? '00').padStart(2, '0');
		const SS = (m[6] ?? '00').padStart(2, '0');

		return `${yyyy}-${mm}-${dd} ${HH}:${MI}:${SS}`;
	}

	function adjustQueryPreviewType(type: string): string {
		switch (type) {
			case 'VARCHAR':
				return 'TEXT';
			case 'DOUBLE':
				return 'FLOAT';
			default:
				return type;
		}
	}

	function getWidthList(headers: any[]) {
		if (props.onBoard) {
			const list = Array<number>(headers.length).fill(TABLE.COL_WIDTH);
			orderList.value = list.map((_, index) => index);
			return list;
		}
		const local = metadataStore.activeWidthList?.list;
		const diff = local?.length === headers.length;
		const list = diff ? local : Array<number>(headers.length).fill(TABLE.COL_WIDTH);
		if (!diff) updateWidth(list);
		orderList.value = metadataStore.activeWidthList?.order;
		return list;
	}

	function executeSelectedOption(
		methodName: keyof {
			toggleEditAttributeModal: () => void;
			openDeleteAttributeModal: () => void;
		}
	) {
		const methods = {
			toggleEditAttributeModal,
			openDeleteAttributeModal
		};

		methods[methodName]();
	}

	function openDeleteAttributeModal() {
		toggleAttributePopup(false);
		confirmModalController.value = true;
	}

	async function deleteAttribute() {
		confirmModalExecutionLoading.value = true;
		try {
			const { error } = await freeDbService.deleteFreeDbColumn(
				props.dimension.name,
				selectedAttribute.value.name,
				props.dimension?.dataSourceId || 1
			);
			if (!error) {
				confirmModalController.value = false;
				loadDimensionContent({ displayLoadingFrame: false });
				metadataStore.fetchDatabaseList();
			} else throw error;
		} catch (error) {
			const cube = ['Number', 'SingleLineText', 'Text', 'Date'].includes(
				selectedAttribute.value.dimensionAttributeType
			);
			toastNotify(
				merge
					? cube
						? 'DATABASE.used_cube'
						: 'DATABASE.used_dim'
					: (error as CustomError).message ??
							'DIMENSIONS.attributes.delete_attribute_error'
			);
		}
		confirmModalExecutionLoading.value = false;
	}

	function pasteData() {
		const selection = supersheet.value?.getActiveCell();

		navigator.clipboard.readText().then((resp) => {
			const dataList = resp
				.split(useOperationalSystemType().mac ? '\n' : '\r\n')
				.map((item) => item.split('\t'));
			interface dynamicEntry {
				[key: string]: string | number;
			}
			const dataEntry: dynamicEntry = {};
			let listRow = 0;
			if (selection && selection.rowStart !== null && selection.colStart !== null) {
				const rowAnd = selection.rowStart + dataList.length - 1;
				const colAnd = selection.colStart + dataList[0].length - 1;
				for (let row = selection.rowStart; row <= rowAnd; row++) {
					let listCol = 0;
					if (!list.value[row]) break;
					for (let col = selection.colStart; col <= colAnd; col++) {
						const header = nestedHeaders.value[col];
						if (!header) break;
						const colType = header.dimensionAttributeType;
						const isNumber = ['Number', 'INT', 'FLOAT'].includes(colType);
						const isText = ['Text', 'TEXT'].includes(colType);
						if (isNumber || isText) {
							const listValue = dataList[listRow][listCol];
							const value = isNumber ? Number(listValue) : listValue;
							if (value || value === 0) dataEntry[`${row}-${col}`] = value;
						}
						listCol++;
					}
					listRow++;
				}
				applyEntry(dataEntry);
			}
		});
	}

	function copyData() {
		const selection = supersheet.value?.getActiveCell();
		if (
			selection &&
			selection.rowAnd !== null &&
			selection.rowStart !== null &&
			selection.colAnd !== null &&
			selection.colStart !== null
		) {
			let text = '';
			for (let row = selection.rowStart; row <= selection.rowAnd; row++) {
				for (let col = selection.colStart; col <= selection.colAnd; col++) {
					const value = list.value[row][col];
					if (Array.isArray(value))
						value.forEach((item) => {
							const value = item.name ?? item.label ?? item;
							if (typeof value !== 'object') text = text + ' ' + value;
						});
					else
						text = text + (value?.name ?? value?.label ?? (value === null ? '' : value));
					if (col !== selection.colAnd) text = text + '\t';
				}
				if (row !== selection.rowAnd) text = text + '\r\n';
			}
			navigator.clipboard.writeText(text);
		}
	}

	function updateWidth(list: number[]) {
		metadataStore.saveWidthList(list);
	}

	function addColumnMenu() {
		metadataStore.setColToEdit(null);
		useDialogStore().switchDialog('databaseColumnController', true);
	}

	defineExpose({
		loadDimensionContent
	});
</script>
<style scoped>
	:deep(.header-flex):has(.visualAutomationType) {
		background-color: #eff1f7;
	}
	.header {
		font-weight: 400;
		font-size: 14px;
		color: #0e1428;
		font-family: 'inter';
	}

	.title {
		color: #1b2139;
		font-size: 16px;
		font-weight: 500;
		margin-bottom: 10px;
	}

	.cancel {
		margin-right: 22px;
		color: #5d6585;
		font-size: 14px;
		font-weight: 500;
		cursor: pointer;
		margin-left: auto;
	}

	.att-title {
		color: var(--grey-700);
		font-weight: 500;
		font-size: 14px;
	}
	.root + :deep(span) {
		position: relative;
		right: 30px;
	}

	.alert {
		height: 48px;
		width: 48px;
		border-radius: 28px;
		border: 8px solid #fef3f2;
		background: #fee4e2;
	}
	/* .cancel-button:hover {
		background-color: #f6f7fb;
	}
	:deep(.cancel-button .button-text) {
		color: v-bind(whiteLabelPrimaryTextColor) !important;
	} */
	/* .cancel-button {
		box-shadow: none !important;
		border: 1px solid #c7cbdd;
	}
	.clear-button:hover {
		background-color: #b42318;
	}
	.clear-button {
		box-shadow: none !important;
	} */
	.title {
		color: var(--gray-900, #0e1428);
		text-align: center;
		font-family: Inter;
		font-size: 18px;
		font-style: normal;
		font-weight: 500;
		line-height: 28px;
	}
	:deep(.header-flex:has(.fkHeader)),
	.fkHeader {
		background: var(--blue-50, #eff8ff);
	}
	:deep(.col-content:has(.fkCell)),
	.fkCell {
		background: var(--Blue-25, #f5faff);
	}
</style>
<style>
	.table-att-menu .ant-btn {
		background-color: #7839ee !important;
		font-weight: 400;
		font-size: 14px;
	}
	.table-att-menu .ant-btn[disabled] {
		background-color: #ddd6fe !important;
	}
	.table-att-menu .ant-btn span {
		color: white !important;
		font-weight: 500;
		font-size: 14px;
		font-family: 'inter';
	}
</style>
