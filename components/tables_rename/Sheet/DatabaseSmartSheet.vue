<template>
	<div
		:id="`SmartTable${tableId}`"
		ref="smartTableDivParent"
		class="table-class noselect mb-[32px] overflow-auto"
		:style="cssVars"
		@scroll="updateFixed"
		@mouseenter="clearMouseEvents"
	>
		<div
			v-for="(content, type) of tables"
			:key="type"
			class="flex h-fit min-h-full flex-col"
			:style="{ zIndex: !type ? 1 : 0 }"
			:class="getTableClass(type)"
		>
			<div class="height-fill-available flex w-fit min-w-full flex-col">
				<!-- RENDER HEADER'S CELL -->
				<slot name="headerParent">
					<div
						class="headers-parent"
						:style="{ paddingRight: !type ? '0px' : '10px' }"
					>
						<div class="header-row !w-max">
							<div
								v-for="(order, index) in content.order"
								:key="order"
								class="h-full"
								:class="getHeaderClass(content.header[order], index)"
								@mousedown="type && startOrder(index)"
								@mouseenter="newOrderPosition(type, index)"
								@mouseup="applyReorder(type)"
							>
								<div
									v-if="!type || index >= fixed"
									:style="cellStyle(order)"
									class="h-full"
									:class="{ border: index !== content.header.length - 1 || !!type }"
									@contextmenu.stop.prevent="emitContextHeader(content.header[order], index)"
								>
									<div
										class="header-flex h-full w-full"
										:data-cy="`header-col-${index}`"
									>
										<div
											class="cell-class width-fill-available flex items-end justify-end truncate"
										>
											<slot
												name="header"
												:col="order"
												:item="content.header[order]"
											>
												<span>{{ content.header[order] }}</span>
											</slot>
										</div>
										<div
											v-if="first === -1"
											class="resize"
											@mousedown.stop="startResize($event, index)"
										></div>
									</div>
								</div>
							</div>
							<div
								v-if="addColumn && type && !activeTableReaderId"
								class="h-full"
							>
								<div
									:style="cellStyle(nestedHeaders.length)"
									class="h-full border"
								>
									<div class="header-flex h-full w-full">
										<div
											class="cell-class width-fill-available flex items-end justify-center truncate"
										>
											<slot
												name="addcol"
												:col="nestedHeaders.length"
											>
												<span>+</span>
											</slot>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</slot>
				<div
					class="scroll-div"
					:style="{
						backgroundColor: type ? '#F8F9FD' : '#FFFFFF'
					}"
					:class="{
						contentScroll: type,
						contentFixed: !type
					}"
				>
					<div
						class="virtual-content"
						:style="{ marginRight: type && addColumn ? '80px' : '' }"
					>
						<!-- RENDER ROWS -->
						<div
							v-for="(row, index) in content.contents"
							:key="index"
							:data-cy="`line-${index}`"
							class="row-content"
						>
							<!-- RENDER CELLS -->
							<div
								v-for="(order, idx) in content.order"
								:key="order"
							>
								<div
									v-if="!type || idx >= fixed"
									:id="`ContentCell-${index}-${idx}`"
									:tabIndex="1 + index"
									class="col-content"
									:data-cy="`line-${index}-col-${idx}`"
									:style="cellStyle(order)"
									:class="
										dataEntryClass(
											idx,
											index,
											idx !== row.length - 1 || !!type,
											content.header[order],
											!!row[order]
										)
									"
									@mouseenter="setNewPosition($event, idx, index), newOrderPosition(type, idx)"
									@mouseup="applyReorder(type)"
									@mousedown="emitMouseDown(index, idx, order)"
									@click="focusEntry(idx, index, order)"
									@contextmenu.stop.prevent="emitContextmenu(index, idx)"
									@blur="cleanSelection"
									@focus="focusTab(index, idx)"
								>
									<!-- RENDER A DRAFT TAPUME  -->
									<div
										v-if="
											(hasDraftAlert || isFreeDb
												? content.header[order]?.isRequired
												: idx === 0 && merge) &&
											selectedDraft !== `${idx}-${index}` &&
											row[order] === null
										"
										class="cell--draft z-[2] flex h-full w-full shrink-0 items-center justify-end"
										@click="selectedDraft = `${idx}-${index}`"
									>
										<SvgIcon
											class="mr-2"
											size="15"
											color="grey"
											type="mdi"
											:path="mdiAlert"
										></SvgIcon>
									</div>

									<!-- RENDER A NUMBER/TEXT INPUT IF DATA ENTRY IS ACTIVE -->
									<input
										v-if="isActiveEntry(idx, index, order)"
										:id="`dataEntry-${index}-${idx}`"
										v-model="activeEntry"
										:tabIndex="1 + index"
										:type="getInputType(order)"
										class="content-cell"
										@blur="checkDataChanged(order, index, $event)"
										@keypress.enter="checkKeyEnter(index, idx)"
										@click.stop.prevent
									/>
									<!-- RENDER A CONTENT SCOPED IN PARENT COMPONENT IF DATAENTRY OF INPUT IS ACTIVE -->
									<div
										v-if="!isActiveEntry(idx, index, order)"
										class="content-cell"
									>
										<slot
											:row="index"
											:col="order"
											:item="row[order]"
										>
											{{ cellValue(index, order) }}
										</slot>
									</div>
									<!-- RENDER A SELECT ELEMENT TO GRAB MULTIPLE CELLS -->
									<div
										v-if="isActiveEntryBtn(idx, index, order) && !isActiveEntry(idx, index, order)"
										class="w-0"
									>
										<div
											class="entry-btn"
											:style="entryPosition()"
											@mousedown.stop="startSelection(index, idx)"
										></div>
									</div>

									<div
										v-if="idx === 0 && index === 0 && cellValue(index, order) === '-999'"
										class="flex items-center mr-2 right-0 absolute"
									>
										<a-tooltip
											placement="bottom"
											overlay-class-name="base-tooltip"
										>
											<template #title>
												<span class="base-tooltip-text">{{
													$t('DATABASE.automatically_created_item')
												}}</span>
											</template>

											<svg-icon
												size="16"
												type="mdi"
												:path="mdiInformationOutline"
												color="var(--grey-500)"
												class="content-icon"
											></svg-icon>
										</a-tooltip>
									</div>
								</div>
							</div>
						</div>
						<div
							v-if="addLine && content.contents.length >= rowCount && !activeTableReaderId"
							class="row-content"
							@click="addRow"
						>
							<div
								ref="addButton"
								:tabindex="items.length + 1"
								class="col-content add-button-table w-full cursor-pointer"
								:class="{ border: type && props.nestedHeaders.length > 2 }"
								@keypress.enter="addRow"
								@focus="forcedClearSelection"
							>
								<slot
									v-if="!type && addLine"
									name="footer"
								>
									<SvgIcon
										class="ml-2"
										size="15"
										color="#1B2139"
										type="mdi"
										:path="mdiPlus"
									></SvgIcon>
								</slot>
							</div>
							<!-- RENDER CELLS -->
						</div>
						<div
							v-if="content.contents.length >= rowCount"
							class="row-content"
							:style="{
								height: '160px'
							}"
						>
							<div
								class="h-full w-full"
								:style="{
									backgroundColor: type ? '#F8F9FD' : '#FFFFFF'
								}"
							></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- <div v-if="addLine" class="row-content footer-class" @click="emit('add-row')">
			<slot name="footer"> + </slot>
		</div> -->
		<DatabaseResizer
			:resize="resize"
			:current-width="localColWidthWithMin[orderList[resize.col]]"
			@change-width="setNewWidth"
		/>
		<DatabaseReorder
			:show="second !== -1 && second !== first"
			:reorder-left="reorderLeft"
		></DatabaseReorder>
		<div
			class="border-top absolute bottom-0 z-[4] flex min-h-[32px] w-full items-center bg-[white] flex justify-between"
		>
			<span v-if="!freeDbOnlineTable">
				<span v-if="rowCount === 1">{{ rowCount }} {{ $t('DATABASE.record') }}</span>
				<span v-else>{{ rowCount }} {{ $t('DATABASE.records') }}</span>
			</span>
			<span v-else></span>
			<KnowledgeBaseSelect
				v-if="!isDeveloperWorkspace && isCurrentKnowledgeBase"
				:table-name="tableName"
				:online-tags="onlineTags"
			></KnowledgeBaseSelect>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import type { StyleValue } from 'vue';
	import { mdiAlert, mdiPlus, mdiInformationOutline } from '@mdi/js';
	import DataEntryModel from '~~/models/DataEntry.class';
	import Selection from '~~/models/TableSelection.class';

	const workspaceStore = useWorkspaceStore();
	const { isDeveloperWorkspace, selectedProject } = storeToRefs(workspaceStore);

	type CellType = string | number | LinkRecord | LinkRecord[];
	type HeaderValue = HeaderContent[] | ViewHeaderItem[];

	defineExpose({
		focusCell,
		getActiveCell,
		deleteContent
	});

	const emit = defineEmits([
		'add-row',
		'data-entry',
		'context-menu',
		'context-header',
		'load-data',
		'width-list'
	]);

	const isCurrentKnowledgeBase = computed(() => {
		if(workspaceStore && workspaceStore.workspaceInfo &&
			workspaceStore.workspaceInfo.knowledgeBase &&
			workspaceStore.workspaceInfo.knowledgeBase.knowledgeBaseProjectId
		) {
			return workspaceStore.workspaceInfo.knowledgeBase.knowledgeBaseProjectId === selectedProject.value.id
		}
		return false;
	});

	const props = defineProps({
		isFreeDb: {
			type: Boolean,
			default: false
		},
		tableId: {
			type: Number,
			default: () => 1
		},
		items: {
			type: Array as PropType<Content[]>,
			default: (): Content[] => []
		},
		colWidths: {
			type: Array,
			default: (): number[] => []
		},
		nestedHeaders: {
			type: Array as PropType<HeaderValue>,
			default: (): HeaderValue => []
		},
		cellMinWidth: {
			type: Number,
			default: 87
		},
		rowHeight: {
			type: Number,
			default: 30
		},
		rowCount: {
			type: Number,
			required: true
		},
		enableDataEntry: {
			type: Boolean,
			default: true
		},
		hasDraftAlert: {
			type: Boolean,
			default: false
		},
		addLine: {
			type: Boolean,
			default: false
		},
		addColumn: {
			type: Boolean,
			default: false
		},
		fixed: {
			type: Number,
			required: false,
			default: 0
		},
		freeDbOnlineTable: {
			type: Boolean,
			required: false,
			default: false
		},
		native: {
			type: Boolean,
			required: false,
			default: false
		},
		dataKeyName: {
			type: Object as PropType<DataKeyNames>,
			required: false,
			default: () => {
				return {
					isList: false,
					contentViews: 'contents',
					contentType: 'dataType'
				};
			}
		},
		onBoard: {
			type: Boolean,
			default: false
		},
		orderList: {
			type: Array as PropType<number[]>,
			required: true
		},
		onlineTags: {
			type: Array,
			required: false,
			default: () => []
		}
	});

	const metadataStore = useMetadataStore();
	const { activeTableReaderId } = storeToRefs(metadataStore);
	const { merge: mergeData } = useAuthStore();
	const merge = mergeData && !props.onBoard;
	let dataEntry = new DataEntryModel();
	let entrySelection = new Selection();
	const activeEntry = ref<CellType>('');
	const smartTableDivParent = ref<HTMLDivElement>();
	const smartTableDivContent = ref<HTMLDivElement>();
	const smartTableDivFixed = ref<HTMLDivElement>();
	const addButton = ref();
	const selectedDraft = ref('');
	const updateStyle = ref(1);
	const lastItemsLength = ref(0);
	const first = ref(-1);
	const second = ref(-1);
	const reorderLeft = ref(0);
	const activeTable = computed(
		() => metadataStore.activeMetadataList[metadataStore.activeIndex]
	);
	const localColWidths = computed(() => props.colWidths);
	const resize = reactive({
		active: false,
		col: 0,
		startPosition: 0,
		endPosition: 0,
		offset: 0
	});
	const fixedItems = computed((): any[] => {
		return props.items.map((row) => {
			const list = getRow(row);
			const filteredRow = getFixed(list);
			return filteredRow;
		});
	});
	const orderList = computed(() => props.orderList);
	const databaseIsOnlineTable = computed(
		() => activeTableReaderId?.value || false
	);

	const whiteLabelStrongColor = computed(() => {
		return useWhiteLabel().isSankhyaClient
			? 'var(--sankhya-green-600)'
			: 'var(--violet-500)';
	});

	const selectColor = computed(() => {
		return useWhiteLabel().isSankhyaClient
			? 'var(--sankhya-green-50)'
			: 'var(--violet-200)';
	});

	const selectColorClear = computed(() => {
		return useWhiteLabel().isSankhyaClient
			? 'var(--sankhya-green-50)'
			: 'var(--violet-100)';
	});

	const tables = computed(() => {
		const data = [
			{
				header: getFixed(props.nestedHeaders),
				contents: fixedItems.value,
				order: Array(Math.min(props.fixed, props.nestedHeaders.length))
					.fill(0)
					.map((_, index) => index)
			},
			{
				header: props.nestedHeaders,
				contents: props.items.map((row) => getRow(row)),
				order: orderList.value
			}
		];
		setTimeout(() => goToSelectedAtt());
		return data;
	});

	const tableName = computed(() => {
		return activeTable.value;
	});

	useEventListener(smartTableDivParent, 'mouseup', () => {
		applyMultiselectEntry();
	});
	useEventListener(smartTableDivParent, 'keydown', (event: KeyboardEvent) => {
		if (!dataEntry.active && (event.keyCode === 46 || event.keyCode === 8)) {
			deleteContent();
		}
		if (!(event.metaKey || event.ctrlKey)) {
			const col = Number(entrySelection.colStart);
			const row = Number(entrySelection.rowStart);
			const realCol = props.orderList[col];
			if (
				!(col === 0 && !!merge && !!cellValue(row, realCol)) &&
				!props.nestedHeaders[realCol]?.isDynamic &&
				!databaseIsOnlineTable.value &&
				!dataEntry.active &&
				entrySelection.active &&
				((event.keyCode >= 48 && event.keyCode <= 57) ||
					(event.keyCode >= 65 && event.keyCode <= 90) ||
					(event.keyCode >= 96 && event.keyCode <= 105) ||
					event.keyCode === 32 ||
					event.keyCode === 13 ||
					(event.keyCode >= 186 && event.keyCode <= 192) ||
					(event.keyCode >= 219 && event.keyCode <= 222))
			) {
				focusContent(row, col);
				const validValue = !(
					(props.nestedHeaders[realCol].dimensionAttributeType ||
						props.nestedHeaders[realCol].dataType) === 'Number' && isNaN(Number(event.key))
				);
				if (event.keyCode !== 13 && validValue && event.key !== 'Dead')
					activeEntry.value = event.key;
			}
		} else if (event.keyCode === 13 && props.rowCount === props.items.length) {
			forcedClearSelection();
			emit('add-row');
		}
		if (event.keyCode >= 37 && event.keyCode <= 40 && !dataEntry.active) {
			event.stopPropagation();
			event.preventDefault();
			const maxRow = props.items.length - 1;
			const maxCol = tables.value[1].contents[0].length - 1;
			if (entrySelection.active) {
				let col = Number(entrySelection.colStart);
				let row = Number(entrySelection.rowStart);
				if (event.keyCode === 37) col -= 1;
				else if (event.keyCode === 38) row -= 1;
				else if (event.keyCode === 39) col += 1;
				else if (event.keyCode === 40) row += 1;
				if (cellValue(row, col) !== undefined) focusTab(row, col);
				else if (col < 0) focusTab(row, maxCol);
				else if (row < 0) {
					if (props.rowCount === props.items.length) focusTab(maxRow, col);
				} else if (col > maxCol) focusTab(row, 0);
				else if (row > maxRow) focusTab(0, col);
			} else if (document.activeElement?.classList.contains('add-button-table')) {
				if (event.keyCode === 37) focusTab(maxRow, 0);
				else if (event.keyCode === 38) focusTab(maxRow, 0);
				else if (event.keyCode === 39) focusTab(maxRow, maxCol);
				else if (event.keyCode === 40) focusTab(0, 0);
			}
		}
	});

	onMounted(() => {
		if (smartTableDivParent.value) {
			smartTableDivContent.value = smartTableDivParent.value.querySelector(
				'.contentScroll'
			) as HTMLDivElement;
			smartTableDivFixed.value = smartTableDivParent.value.querySelector(
				'.contentFixed'
			) as HTMLDivElement;
		}
	});

	watch(
		() => metadataStore.activeAttribute,
		() => {
			goToSelectedAtt();
		}
	);

	function goToSelectedAtt() {
		if (
			activeTable.value &&
			activeTable.value.id === props.tableId &&
			smartTableDivParent.value
		) {
			const index = props.nestedHeaders.findIndex(
				(header: any) =>
					(header.id?.id ?? header.id ?? header.dimensionId) ===
					metadataStore.activeAttribute.attId
			);
			if (index > -1 && props.fixed <= index) {
				const fixedWidth = (smartTableDivParent.value?.firstElementChild as any)
					?.offsetWidth;
				const content = smartTableDivContent.value?.children[0]?.children[0]?.children[
					orderList.value[index]
				] as any;
				if (content)
					smartTableDivParent.value.scrollLeft = content.offsetLeft - fixedWidth;
			}
		}
	}

	function getHeaderClass(header: any, index: number) {
		return {
			selected: isSelected(header),
			reorder: index === first.value
		};
	}

	function isSelected(header: any) {
		const attributeId = props.isFreeDb
			? header.name
			: header.id?.id ?? header.id ?? header.dimensionId ?? header.cubeId;
		const isActiveTable = activeTable.value?.id === props.tableId;
		const isActiveAtt =
			(props.isFreeDb
				? false
				: metadataStore.activeAttribute.isCube === header?.id) ||
			attributeId === metadataStore.activeAttribute.attId;
		return isActiveTable && isActiveAtt;
	}

	function deleteContent() {
		applyValues(true);
		setUpdateStyle();
	}

	function getFixed(list: any[]) {
		return list.slice(0, props.fixed);
	}

	const localColWidthWithMin = computed((): number[] => {
		const min = props.cellMinWidth ?? 0;
		return (localColWidths.value as number[]).map((val) => (min > val ? min : val));
	});

	const cssVars = computed(
		(): StyleValue => ({
			'--rowHeight': `${props.rowHeight}px`
		})
	);

	function updateFixed(event: any) {
		if (event?.target) {
			if (
				event?.target.scrollTop &&
				event?.target.scrollTop + event?.target.offsetHeight >
					event?.target.scrollHeight - 20 &&
				lastItemsLength.value !== props.items.length
			) {
				lastItemsLength.value = Number(props.items.length);
				emit('load-data');
			}
		}
	}

	function emitContextmenu(row: number, col: number) {
		emit('context-menu', `${row}-${col}`);
	}

	function getRow(row: any) {
		return props.dataKeyName?.isList ? row : row[props.dataKeyName.contentViews];
	}

	function cellStyle(col: number) {
		return {
			width: Math.max(localColWidthWithMin.value[col] || 80, 0) + 'px',
			minWidth: Math.max(localColWidthWithMin.value[col] || 80, 0) + 'px'
		};
	}

	function setNewPosition(e: MouseEvent, idx: number, index = 0) {
		const col = idx;
		if (entrySelection?.active) {
			if (e.buttons) {
				const dataSelection = dataEntry.selection;
				const row = index;
				if (!dataEntry.active) {
					setSelectionPosition(col, row, dataEntry.selection, dataEntry.rootSelection);
				}
				setSelectionPosition(col, row, entrySelection, dataSelection);
				setUpdateStyle();
			}
		}
	}

	function setSelectionPosition(
		col: number,
		row: number,
		entrySelection: Selection,
		dataSelection: Selection
	) {
		entrySelection.colAnd = Math.max(
			col,
			dataSelection.colStart ?? 0,
			dataSelection.colAnd ?? 0
		);
		entrySelection.rowAnd = Math.max(
			row,
			dataSelection.rowStart ?? 0,
			dataSelection.rowAnd ?? 0
		);
		entrySelection.colStart = Math.min(
			col,
			dataSelection.colStart ?? 0,
			dataSelection.colAnd ?? 0
		);
		entrySelection.rowStart = Math.min(
			row,
			dataSelection.rowStart ?? 0,
			dataSelection.rowAnd ?? 0
		);
	}

	function isBetweenSelection(
		positionCol: number,
		positionRow: number,
		list: Selection = entrySelection
	) {
		const entrySelection = list;
		const between =
			(entrySelection.colAnd ?? 0) >= positionCol &&
			(entrySelection.colStart ?? 0) <= positionCol &&
			(entrySelection.rowAnd ?? 0) >= positionRow &&
			(entrySelection.rowStart ?? 0) <= positionRow;
		return between;
	}

	function dataEntryClass(
		idx: number,
		index: number,
		border: boolean,
		header: HeaderContent,
		hasItem = false
	) {
		const isAutoIncrement =
			props.isFreeDb && !props.freeDbOnlineTable
				? header?.inPrimaryKey
				: ((header.isNative ||
						header.dimensionId ||
						(merge && (idx === 0 || (idx === 1 && props.native)))) &&
						hasItem) ||
				  header.isDynamic ||
				  header?.isNativeColumnDb ||
				  props.freeDbOnlineTable ||
				  databaseIsOnlineTable.value;
		let style = {};
		const positionCol = idx;
		const positionRow = index;
		const row = positionRow;
		const attribute = `${row}-${idx}`;
		const dataSelection = dataEntry.selection;
		const between =
			entrySelection.active && isBetweenSelection(positionCol, positionRow);
		const betweenActive = isBetweenSelection(
			positionCol,
			positionRow,
			dataSelection
		);
		style = {
			isAutoIncrement,
			border,
			dataEntry: !dataEntry.multiSelect && dataEntry.active === attribute,
			active: dataEntry.active === attribute,
			left:
				(between && entrySelection.colStart === positionCol) ||
				(betweenActive && dataSelection.colStart === positionCol),
			top:
				(between && entrySelection.rowStart === positionRow) ||
				(betweenActive && dataSelection.rowStart === positionRow),
			right:
				(between && entrySelection.colAnd === positionCol) ||
				(betweenActive && dataSelection.colAnd === positionCol),
			bottom:
				(between && entrySelection.rowAnd === positionRow) ||
				(betweenActive && dataSelection.rowAnd === positionRow),
			betweenActive: dataSelection.active && betweenActive,
			selected: isSelected(header),
			reorder: idx === first.value
		};
		return updateStyle.value && style;
	}

	function applyReorder(type: number) {
		if (second.value !== -1 && type && first.value !== second.value) {
			const order = orderList.value;
			const oldPosition = order.splice(first.value, 1);
			order.splice(second.value, 0, Number(oldPosition));
		}
		first.value = -1;
		second.value = -1;
	}

	function newOrderPosition(type: number, col: number) {
		if (first.value !== -1) {
			if (type) {
				second.value = col;
				const el = document.getElementById(`ContentCell-${0}-${col}`);
				if (el) {
					const width = first.value > second.value ? 0 : el.offsetWidth - 2;
					reorderLeft.value =
						el.offsetLeft + width - (smartTableDivParent.value?.scrollLeft || 0);
				}
			} else second.value = -1;
		}
	}

	function startOrder(col: number) {
		first.value = col;
	}

	function startResize(ev: any, col: number) {
		if (smartTableDivParent.value) {
			const { x } = smartTableDivParent.value.getBoundingClientRect();
			setTimeout(() => {
				resize.active = true;
				resize.col = col;
				resize.startPosition = ev.x - x + 3;
				resize.endPosition = ev.x - x + 3;
				resize.offset = x;
			});
		}
	}

	function clearMouseEvents(ev: any) {
		if (!ev.buttons) {
			first.value = -1;
			second.value = -1;
		}
	}

	function setNewWidth({ col, width }: { col: number; width: number }) {
		resize.active = false;
		localColWidths.value[orderList.value[col]] = width;
		// emit('width-list', localColWidths.value);
	}

	function focusTab(row: number, col: number) {
		selectedDraft.value = `${col}-${row}`;
		document.getElementById(`ContentCell-${row}-${col}`)?.focus();
		setNewSelection(row, col);
		setUpdateStyle();
	}

	function setNewSelection(row: number, col: number) {
		entrySelection.active = `${row}-${col}`;
		entrySelection.colStart = col;
		entrySelection.rowStart = row;
		entrySelection.colAnd = col;
		entrySelection.rowAnd = row;
		dataEntry.selection = { ...entrySelection };
		dataEntry.rootSelection = { ...entrySelection };
	}

	function focusCell(index: number, col: number) {
		selectedDraft.value = `${col}-${index}`;
		focusContent(index, col);
		const table = smartTableDivContent.value;
		if (table && !props.onBoard) {
			table.scrollTop = table.scrollHeight;
		}
	}

	function focusEntry(col: number, row: number, order: number) {
		if (props.enableDataEntry && dataEntry.input) {
			const grid = `${row}-${col}`;
			dataEntry.active = grid;
			activeEntry.value = dataEntry[grid] = cellValue(row, order) as CellType;
			setUpdateStyle();
			setTimeout(() => {
				// tratar ref com atributo dinamico
				document.getElementById(`dataEntry-${row}-${col}`)?.focus();
			});
		}
	}

	function isActiveEntryBtn(col: number, index: number, order: number) {
		const row = index;
		const entrySelection = dataEntry.selection;
		const header = props.nestedHeaders[order];
		return (
			!header?.referencingMe &&
			updateStyle.value &&
			entrySelection.rowAnd === row &&
			entrySelection.colAnd === col
		);
	}

	function cellValue(index: number, item: number, checkEntry = true) {
		const entry = checkEntry && dataEntryValue(item, index);
		const cellValue = tables.value[1]?.contents[index][item];
		if (cellValue === undefined) return undefined;
		const rawCellValue =
			cellValue?.metricValue || cellValue?.metricValue === null
				? cellValue?.metricValue
				: cellValue;
		return updateStyle.value && (entry || rawCellValue);
	}

	function dataEntryValue(col: number, index: number) {
		const row = index;
		return dataEntry[`${row}-${col}`];
	}

	function startSelection(row: number, col: number) {
		dataEntry.multiSelect = true;
		dataEntry.selection = { ...entrySelection };
		dataEntry[`${row}-${col}`] = tables.value[1].contents[row][col];
		if (!dataEntry.active) {
			dataEntry.active = true;
		}
	}

	function isActiveEntry(col: number, index: number, order: number) {
		const row = index;
		const header = props.nestedHeaders[order];
		return (
			(props.isFreeDb
				? !header.inPrimaryKey || (header.inPrimaryKey && !header.isAutoIncrement)
				: !header?.isDynamic &&
				  !databaseIsOnlineTable.value &&
				  !(
						(col === 0 || (col === 1 && props.native)) &&
						!!merge &&
						!!cellValue(row, col)
				  ) &&
				  !header?.referencingMe &&
				  !header?.isNative &&
				  useEnableDataEntryTypes()[
						header?.dimensionAttributeType ?? header?.dataType ?? ''
				  ] &&
				  updateStyle.value &&
				  !dataEntry.multiSelect) && dataEntry.active === `${row}-${col}`
		);
	}

	function checkDataChanged(col: number, index: number, focusEvent: FocusEvent) {
		updateData(col, index);
		const row = index;
		const grid = `${row}-${col}`;
		if (dataEntry[grid] === cellValue(index, col, false)) {
			delete dataEntry[grid];
		} else {
			applyDataEntry({ [grid]: dataEntry[grid] });
		}
		dataEntry.active = null;
		dataEntry.input = false;
		setUpdateStyle();
		cleanSelection(focusEvent);
	}

	/**
	 * @remarks
	 * the return of an empty input is always an empty string,
	 * this function guarantees a null return if the input is number and is empty
	 */
	function getRawData(col: number) {
		return getInputType(col) === 'number'
			? activeEntry.value === ''
				? null
				: activeEntry.value
			: activeEntry.value;
	}

	function updateData(col: number, row: number) {
		dataEntry[`${row}-${col}`] = getRawData(col);
	}

	function emitMouseDown(index: number, idx: number, order: number) {
		const row = index;
		const col = idx;
		const entry = dataEntry.selection;
		const header = props.nestedHeaders[order];
		const isInput =
			idx >= (entry.colStart ?? -1) &&
			idx <= (entry.colAnd ?? -1) &&
			index >= (entry.rowStart ?? -1) &&
			index <= (entry.rowAnd ?? -1);
		if (
			isInput &&
			!header.isNative &&
			!header.referencingMe &&
			!(
				(col === 0 || (col === 1 && props.native)) &&
				!!merge &&
				!!cellValue(row, order)
			) &&
			useEnableDataEntryTypes()[
				header?.dimensionAttributeType ?? header?.dataType ?? ''
			] &&
			!header?.isDynamic &&
			!databaseIsOnlineTable.value
		) {
			dataEntry.input = true;
		}
		setNewSelection(row, col);
		setUpdateStyle();
	}

	function setUpdateStyle() {
		updateStyle.value++;
	}

	function applyMultiselectEntry() {
		if (dataEntry.multiSelect && entrySelection !== dataEntry.selection) {
			applyValues();
			dataEntry.selection = { ...entrySelection };
			dataEntry.active = null;
			dataEntry.multiSelect = false;
			setUpdateStyle();
		}
	}

	function getSelectionValues(clear = false) {
		const valuesList = [];
		const selection = dataEntry.selection;
		if (selection.active) {
			for (let row = selection.rowStart ?? 0; row <= (selection.rowAnd ?? 0); row++) {
				const rowList = [];
				for (let col = selection.colStart ?? 0; col <= (selection.colAnd ?? 0); col++) {
					const realCol = props.orderList[col];
					const type =
						props.nestedHeaders[realCol]?.dimensionAttributeType ??
						props.nestedHeaders[realCol]?.dataType;
					const value = clear
						? type.includes('Text')
							? ''
							: null
						: cellValue(row, realCol);
					rowList.push(value);
				}
				valuesList.push(rowList);
			}
		}
		return valuesList;
	}

	function applyValues(clear = false) {
		const values = getSelectionValues(clear);
		const dataSelection = dataEntry.selection;
		const colStart = Number(dataEntry.rootSelection.colStart);
		const sameColStart = dataSelection.colStart === entrySelection.colStart;
		const sameRowStart = dataSelection.rowStart === entrySelection.rowStart;
		const rowLength = values.length;
		const colLength = values[0].length;
		const rowDiff =
			((entrySelection.rowAnd ?? 0) - (entrySelection.rowStart ?? 0) + 1) % rowLength;
		const colDiff =
			((entrySelection.colAnd ?? 0) - (entrySelection.colStart ?? 0) + 1) % colLength;
		const rowInitial = sameRowStart ? 0 : rowDiff ? rowLength - rowDiff : 0;
		const colInitial = sameColStart ? 0 : colDiff ? colLength - colDiff : 0;
		const data: DataEntry = {};
		let rowIdx = rowInitial;
		let colIdx = colInitial;
		for (
			let row = entrySelection.rowStart ?? 0;
			row <= (entrySelection.rowAnd ?? 0);
			row++
		) {
			for (
				let col = entrySelection.colStart ?? 0;
				col <= (entrySelection.colAnd ?? 0);
				col++
			) {
				const realCol = props.orderList[col];
				const startHeader = props.nestedHeaders[props.orderList[colStart + colIdx]];
				const type = startHeader.dimensionAttributeType;
				const header = props.nestedHeaders[realCol];
				if (
					!header.isNative &&
					!header.referencingMe &&
					!(
						(col === 0 || (col === 1 && props.native)) &&
						!!merge &&
						!!cellValue(row, realCol)
					) &&
					!header?.isDynamic &&
					!databaseIsOnlineTable.value
				) {
					const colType = header.dimensionAttributeType ?? '';
					const value = values[rowIdx][colIdx];
					if (
						header.id === startHeader.id ||
						((colType === type || clear) &&
							!(colType.includes('Link') || colType.includes('Choice')))
					) {
						const attribute = `${row}-${realCol}`;
						if (value !== cellValue(row, realCol, false)) {
							dataEntry[attribute] = value;
						}
						data[attribute] = value;
					}
				}
				colIdx++;
				if (colLength === colIdx) colIdx = 0;
			}
			rowIdx++;
			colIdx = colInitial;
			if (rowLength === rowIdx) rowIdx = 0;
		}
		applyDataEntry(data);
	}

	function applyDataEntry(dataEntry: object) {
		emit('data-entry', dataEntry);
	}

	function entryPosition() {
		return {
			bottom: `-${(props.rowHeight ?? 30) / 2}px`,
			right: `${3}px`
		};
	}

	function cleanSelection(e: FocusEvent) {
		const target = e.relatedTarget as Element;
		if (!e.relatedTarget || !target.closest('.virtual-content')) {
			forcedClearSelection();
		}
	}

	function forcedClearSelection() {
		selectedDraft.value = '';
		dataEntry = new DataEntryModel();
		entrySelection = new Selection();
		setUpdateStyle();
	}

	function focusContent(index: number, idx: number) {
		const header = props.nestedHeaders[idx];
		const realCol = props.orderList[idx];
		emitMouseDown(index, idx, realCol);
		if (
			useEnableDataEntryTypes()[
				header?.dimensionAttributeType ?? header?.dataType ?? ''
			]
		)
			dataEntry.input = true;
		focusEntry(idx, index, realCol);
	}

	function getInputType(idx: number) {
		return ['Number', 'INT', 'FLOAT'].includes(
			`${props.nestedHeaders[idx][props.dataKeyName.contentType]}`
		)
			? 'number'
			: 'text';
	}

	function emitContextHeader(item: any, idx: number) {
		emit('context-header', { item, idx });
	}

	function getActiveCell() {
		return dataEntry.selection;
	}

	function checkKeyEnter(index: number, col: number) {
		const nextIndex = index + 1;
		if (props.items[nextIndex]) focusTab(nextIndex, col);
		else addButton.value[0].focus();
	}

	function getTableClass(type: number) {
		return {
			'width-fill-available': type,
			sticky: !type,
			'left-0': !type,
			'bg-white': !type,
			border: !type
		};
	}

	function addRow() {
		(document.activeElement as HTMLInputElement)?.blur();
		emit('add-row');
	}
</script>

<style scoped>
	* {
		outline: unset;
	}
	input {
		padding-left: 0.5rem;
		padding-right: 0.5rem;
		font-family: 'inter';
		font-size: 12px;
	}
	.table-class {
		max-height: -webkit-fill-available;
		max-width: -webkit-fill-available;
		width: inherit;
		height: inherit;
		font-family: 'inter';
		display: flex;
		background-color: #f8f9fd;
	}

	.scroll-div {
		height: -webkit-fill-available;
		width: -webkit-fill-available;
		display: flex;
	}

	.virtual-content {
		display: flex;
		background: white;
		flex-direction: column;
		height: fit-content;
	}

	.row-content {
		display: flex;
		height: var(--rowHeight);
		min-height: var(--rowHeight);
		background-color: #fff;
		font-size: var(--fontSize);
		align-items: center;
		width: 100%;
		color: #1b2139;
	}

	.header-row {
		font-weight: bold;
		display: flex;
		background-color: var(--headerColor);
		color: #1b2139;
		font-size: var(--headerFontSize);
		align-items: center;
		background-color: #f6f7fb;
	}

	.content-cell {
		min-width: inherit;
		font-family: 'Inter';
		font-weight: 400;
		font-size: 14px;
		width: inherit;
		color: #0e1428;
		@apply truncate;
		/* padding: 0px 5px; */
	}

	.headers-parent {
		border-bottom: 1px solid #e7e8f0;
		background-color: #f8f9fd;
		position: sticky;
		min-width: fit-content;
		display: flex;
		min-height: 32px;
		top: 0px;
		z-index: 3;
	}

	.header-cell {
		margin-right: 15px;
		padding-left: 10px;
	}

	.dataEntry.active {
		border: 1px solid #abbad0;
		z-index: 1;
	}

	.hasEntry {
		background: rgb(239, 230, 251);
	}

	.dataEntry {
		display: flex;
		/* background: rgb(239, 230, 251); */
		justify-content: flex-start;
		align-items: center;
	}

	.dataEntry span {
		height: fit-content;
	}

	.dataEntry input {
		width: -webkit-fill-available;
		height: inherit;
		border: none;
		background: transparent;
	}

	.dataEntry input:focus {
		outline: none;
	}

	.col-content {
		position: relative;
		display: flex;
		align-items: center;
		height: var(--rowHeight);
		width: inherit;
		border-bottom: 1px solid #e7e8f0;
	}

	.cell--draft {
		position: absolute;
	}

	.col-content.bottom.betweenActive .content-cell {
		transform: translateY(1px);
	}
	.col-content.top.betweenActive .content-cell {
		transform: translateY(-1px);
	}
	.col-content.top.bottom.betweenActive .content-cell {
		transform: translateY(0px);
	}
	.col-content.left.betweenActive .content-cell {
		transform: translateX(-2px);
	}
	.col-content.left.bottom.betweenActive .content-cell {
		transform: translate(-2px, 1px) !important;
	}
	.col-content.left.top.betweenActive .content-cell {
		transform: translate(-2px, -1px) !important;
	}
	.col-content.left.top.bottom.betweenActive .content-cell {
		transform: translateX(-2px) !important;
	}

	.col-content.left:not(.betweenActive) {
		border-left: 1px dashed v-bind(whiteLabelStrongColor);
	}

	.col-content.right:not(.betweenActive) {
		border-right: 1px dashed v-bind(whiteLabelStrongColor);
	}

	.col-content.top:not(.betweenActive) {
		border-top: 1px dashed v-bind(whiteLabelStrongColor);
	}

	.col-content.bottom:not(.betweenActive) {
		border-bottom: 1px dashed v-bind(whiteLabelStrongColor);
	}

	.col-content.left.betweenActive {
		border-left: 2px solid v-bind(whiteLabelStrongColor);
	}

	.col-content.right.betweenActive {
		border-right: 2px solid v-bind(whiteLabelStrongColor) !important;
	}

	.col-content.top.betweenActive {
		border-top: 2px solid v-bind(whiteLabelStrongColor);
	}

	.col-content.bottom.betweenActive {
		border-bottom: 2px solid v-bind(whiteLabelStrongColor);
	}

	.entry-btn {
		background: v-bind(whiteLabelStrongColor);
		position: relative;
		width: 7px;
		height: 7px;
		border: 1px solid white;
		cursor: crosshair;
		z-index: 3;
		border-radius: 100%;
	}

	.footer-class {
		position: sticky;
		left: 0px;
	}
	.header-flex {
		justify-content: center;
		align-items: center;
		display: flex;
		position: relative;
		min-height: 32px;
	}
	.border {
		border-right: 1px solid #e7e8f0;
	}
	.border-top {
		border-top: 1px solid #e7e8f0;
	}
	.border-top span {
		margin-left: 8px;
		color: #1b2139;
	}
	.contentFixed .virtual-content {
		width: 100%;
	}
	.isAutoIncrement {
		background: #f6f7fb;
	}
	.add-button-table:focus {
		border: 2px solid v-bind(whiteLabelStrongColor) !important;
	}
	.resize {
		display: none;
		width: 5px;
		height: inherit;
		background: v-bind(whiteLabelStrongColor);
		position: absolute;
		right: 0;
		cursor: ew-resize;
	}
	.header-flex:hover .resize {
		display: unset;
	}
	.selected {
		background-color: v-bind(selectColor);
	}
	.col-content.selected {
		background-color: v-bind(selectColorClear);
	}
	.reorder {
		background-color: #f5f3ff !important;
	}
	.header-row .reorder {
		background-color: #ecebfb !important;
	}
</style>
