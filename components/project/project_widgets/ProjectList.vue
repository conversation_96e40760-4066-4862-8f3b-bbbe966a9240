<template>
	<div
		class="flex flex-col gap-4 h-full"
		:class="{ 'flex w-full': operational.isMobile }"
	>
		<div
			class="flex justify-between px-[40px]"
			:class="{ '!px-4 min-w-[80px]': operational.isMobile }"
		>
			<a-input
				v-model:value="searchTerm"
				type="text"
				class="h-[40px] !rounded-lg w-[240px]"
				:class="{ 'w-full': operational.isMobile }"
				:placeholder="$t('GLOBAL.search_projects')"
			>
				<template #prefix>
					<svgo-lab-search
						color="var(--grey-500)"
						class="w-[20px] h-[20px]"
					/>
				</template>
			</a-input>

			<div
				v-if="!operational.isMobile && !openPlaygroundMode"
				class="flex gap-4 z-[5]"
			>
				<MitraButton
					v-if="isSankhyaClient && useSankhyaStore().isAdminOrMasterUser()"
					:text="'Configurar chave AI Copilot'"
					text-color="var(--grey-700)"
					outlined
					@click="
						useDialogStore().dataToDialog('superAIBYOKModalData', {
							visible: true,
							byWorkspace: true
						})
					"
					><template #preffix>
						<HugeiconsIcon
							:icon="Key01Icon"
							color="var(--grey-800)"
							class="mr-2"
							:size="20" /></template
				></MitraButton>

				<MitraButton
					v-if="isSankhyaClient && useSankhyaStore().isAdminOrMasterUser()"
					:text="'Membros'"
					text-color="var(--grey-700)"
					outlined
					@click="openAccessController"
					><template #preffix>
						<HugeiconsIcon
							:icon="UserMultiple02Icon"
							color="var(--grey-800)"
							class="mr-2"
							:size="20" /></template
				></MitraButton>

				<CreateProjectButton />
			</div>
		</div>
		<div class="overflow-auto">
			<div
				class="grid gap-4 pb-8 card-wrapper pl-[40px]"
				:class="{
					'flex-nowrap flex-col': layoutType === 1,
					isCorporate: isCorporate,
					'!px-4 !w-full': operational.isMobile
				}"
				:style="getCardWrapperStyle"
			>
				<MitraCard
					v-for="(project, idx) in projectList"
					:key="`project-card-${idx}`"
					class="!h-[130px] justify-between"
					:class="{ 'w-[100vw]': operational.isMobile && mobileLabSideBarOpened }"
					:item="project"
					:layout-type="layoutType"
					:title="project.name"
					:sub-title="`${project.collaboratorProfile.length} ${$t(
						'MEMBERS.members',
						project.collaboratorProfile.length - 1
					)}`"
					enabled-published-validator
					:project-info="workspaceProjectsInfo?.[project.id]"
					:loading="isProjectsInfoLoading"
					@click-on-card="handleCardClick(project, $event)"
					@hover="handleCardHover($event, idx)"
				>
					<template #title>
						<div class="flex items-center gap-2 w-full">
							<span
								v-if="!isSankhyaClient"
								:title="project.name"
								class="font-medium text-start text-sm text-grey-800 truncate"
							>
								{{ project.name }}</span
							>
							<template v-else>
								<span
									v-if="renamingSkillsIdx !== idx"
									:title="project.name"
									class="w-full font-medium text-start text-sm text-grey-800 truncate"
								>
									{{ project.name }}</span
								>
								<a-input
									v-else
									ref="input"
									v-model:value="renamingSkillText"
									v-focus
									class="input-rename w-[60%]"
									@mousedown.stop
									@click.stop
									@blur="renameSkillByInput(project)"
									@press-enter="$event.target.blur()"
								/>
							</template>
							<template v-if="project?.projectDnsPublished?.length">
								<img
									v-if="project.dnsEnabled"
									width="20px"
									height="20px"
									class="filter-violet-600"
									src="assets/icons/to_replace/cloud-upload.svg?componentText"
								/>
								<HugeiconsIcon
									v-else
									:icon="Loading03Icon"
									:size="20"
									title="Publicando aplicação"
									class="animate-spin shrink-0"
									color="var(--primary-600)"
									variant="solid"
								/>
							</template>

							<HugeiconsIcon
								v-if="KnowledgeBaseProjectId === project.id"
								:icon="AiBrain04Icon"
								color="var(--violet-600)"
								class="shrink-0"
								size="20"
							></HugeiconsIcon>
						</div>
					</template>
					<template #suffix>
						<div
							v-if="!operational.isMobile"
							class="flex flex-col justify-end"
							@click.stop
						>
							<div class="flex gap-2 items-center suffix-wrapper justify-end">
								<div
									v-if="cardHovered === idx"
									class="flex gap-2 icon-container"
								>
									<svgo-lab-play
										v-if="!isSankhyaClient && devProjectPuslishedRule(project)"
										color="var(--grey-500)"
										class="icon w-[20px] h-[20px] hover:transform hover:scale-110"
										@click="openProjectPlayMode(project, $event)"
										@auxclick="openProjectPlayMode(project, $event)"
									/>
									<svgo-lab-dashboard-square-edit
										v-if="project.accessType !== 'BUSINESS' && !openPlaygroundMode"
										color="var(--grey-500)"
										class="icon w-[20px] h-[20px] hover:transform hover:scale-110"
										@click="openProject(project, $event)"
										@auxclick="openProject(project, $event)"
									/>

									<!-- OPTIONS -->
									<a-dropdown
										trigger="click"
										destroy-popup-on-hide
										overlay-class-name="py-2 px-3 mitra-popover-wrapper"
										@visible-change="handleDropdownVisibleChange"
									>
										<div
											class="icon-button icon-more"
											@click.stop
										>
											<svgo-lab-more-vertical
												v-if="project.accessType !== 'BUSINESS' && !openPlaygroundMode"
												color="var(--grey-500)"
												class="icon w-[20px] h-[20px] hover:transform hover:scale-110"
												@click="setSelectedProjectAccordingIndex(idx)"
											/>
										</div>
										<template #overlay>
											<div
												:id="`project-options-${idx}`"
												class="max-w-[240px]"
											>
												<BaseOptionList
													:options="editProjectOptions"
													:item-id="project.id"
													:context-idx="idx"
												/>
											</div>
										</template>
									</a-dropdown>
								</div>
							</div>
						</div>

						<!-- <svgo-lab-more-vertical
							color="var(--grey-500)"
							class="icon w-[20px] h-[20px] hover:transform hover:scale-110"
							@click="openMenu"
						/> -->
					</template>
				</MitraCard>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import {
		Loading03Icon,
		UserMultiple02Icon,
		Key01Icon,
		AiBrain04Icon
	} from '@hugeicons-pro/core-stroke-rounded';
	import { HugeiconsIcon } from '@hugeicons/vue';
	import { POSITION } from 'vue-toastification';

	import {
		mdiAccountMultiple,
		// mdiClose,
		mdiCog,
		mdiContentCopy,
		mdiDeleteOutline,
		mdiPencil,
		mdiPlay,
		mdiShare,
		mdiBrain,
		mdiProgressClose
	} from '@mdi/js';
	import toastNotify from '~/helpers/toast_notify';
	import toastNotifyLarger from '~/helpers/toast_notify_larger';
	import { i18n } from '~~/plugins/i18n';
	const dialogStore = useDialogStore();
	storeToRefs(useMarketplacePublisher());
	const workspaceStore = useWorkspaceStore();
	const { labSideBarOpened, mobileLabSideBarOpened } = storeToRefs(
		useDialogStore()
	);
	const operational = useOperationalSystemType();
	const {
		selectedWorkspace,
		// readonlyProject,
		workspaceProjectsInfo,
		isDeveloperWorkspace,
		temporarySelectedProject,
		selectedProject,
		isProjectsInfoLoading
	} = storeToRefs(workspaceStore);

	const legacyStore = useLegacyStore();

	const isCorporate = computed(
		() => workspaceStore.getWorkspaceWithoutProjects.workspaceType === 'CORPORATE'
	);

	const openPlaygroundMode = computed(() => {
		return useRoute().query?.openOnPlaygroundMode !== undefined;
	});

	const getActiveProject = computed(() => {
		return temporarySelectedProject.value.id
			? temporarySelectedProject.value
			: selectedProject.value;
	});

	const { targetDuplicateWorkspaceList } = storeToRefs(workspaceStore);
	const cardHovered = ref(-1);

	workspaceStore.fetchUserCreateWorkspacesAccess();

	const searchTerm = ref('');
	const renamingProject = ref(false);
	const renamingSkillsIdx = ref<number>(-1);
	const renamingSkillText = ref<string>('');
	const { normalizeText } = useStringFormat();
	const props = defineProps({
		layoutType: {
			type: Number,
			default: 0
		},
		isShared: {
			type: Boolean,
			default: true
		}
	});

	const getCardWrapperStyle = computed(() => {
		const padding = 40;
		const workspaceMenu = 296;
		// const mitraNews = 296;
		const mitraNews = 0;
		const offset =
			padding +
			(!useWhiteLabel().isSankhyaClient && labSideBarOpened.value
				? workspaceMenu
				: 0) +
			(!useWhiteLabel().isSankhyaClient && !isCorporate.value ? mitraNews : 0);
		return {
			width: `calc(100vw - ${offset}px)`
		};
	});

	const projectList = computed(() => {
		return props.isShared
			? filteredSharedProjects.value
			: filteredOwnProjects.value;
	});

	const filteredOwnProjects = computed(() =>
		selectedWorkspace.value.groupedProjects.myOwn.filter((p) =>
			normalizeText(p.name).includes(normalizeText(searchTerm.value))
		)
	);

	const filteredSharedProjects = computed(() =>
		selectedWorkspace.value.groupedProjects.shared.filter((p) =>
			normalizeText(p.name).includes(normalizeText(searchTerm.value))
		)
	);

	function setSelectedProjectAccordingIndex(index: number) {
		const project = projectList.value[index];
		const decoded = useJWT().decodeJWT(useAuthToken().get() as string);
		if (decoded?.tni !== project.id) {
			workspaceStore.swapTenantToken(project.id);
		}
		workspaceStore.setTemporarySelectedProject(project);
	}

	const isToShowMakeStoreToLoggedUser = ref(false);

	verifyIfIsToShowMakeStoreToUser();

	function verifyIfIsToShowMakeStoreToUser() {
		const users = workspaceStore.fetchRestrictedMakeStoreUsers();
		isToShowMakeStoreToLoggedUser.value = users.includes(
			useUserStore().loggedUser?.email as string
		);
	}

	function devProjectPuslishedRule(project: Project) {
		// Deve bloquear apenas no desenvolvedor para projetos nao publicados.
		if (isDeveloperWorkspace.value) {
			if (project.projectDnsPublished) {
				return true;
			} else {
				return false;
			}
		} else {
			return true;
		}
	}

	const { isSankhyaClient } = useWhiteLabel();

	const isCurrentKnowledgeBase = computed(() => {
		if(workspaceStore && workspaceStore.workspaceInfo &&
			workspaceStore.workspaceInfo.knowledgeBase &&
			workspaceStore.workspaceInfo.knowledgeBase.knowledgeBaseProjectId
		) {
			return workspaceStore.workspaceInfo.knowledgeBase.knowledgeBaseProjectId === getActiveProject.value.id
		}
		return false;
	});

	const KnowledgeBaseProjectId = computed(() => {
		if(workspaceStore && workspaceStore.workspaceInfo &&
			workspaceStore.workspaceInfo.knowledgeBase &&
			workspaceStore.workspaceInfo.knowledgeBase.knowledgeBaseProjectId
		) {
			return workspaceStore.workspaceInfo.knowledgeBase.knowledgeBaseProjectId
		}
		return null;
	});

	const editProjectOptions = computed<DropdownOptionContent[]>(() => [
		{
			label: temporarySelectedProject.value.projectDnsPublished
				? useNuxtApp().$translate('GLOBAL.open_app')
				: useNuxtApp().$translate('GLOBAL.open_preview'),
			icon: mdiPlay,
			class: 'm-0',
			hide:
				isSankhyaClient || !devProjectPuslishedRule(temporarySelectedProject.value),
			onClick: () => {
				const { temporarySelectedProject } = useWorkspaceStore();
				openProjectPlayMode(temporarySelectedProject, true);
			}
		},
		{
			label: isSankhyaClient ? 'Renomear' : i18n.global.t('GLOBAL.edit'),
			icon: mdiPencil,
			class: 'm-0',
			onClick: ({ contextIdx }) => {
				if (isSankhyaClient) {
					openRenameCustomSkill({ contextIdx });
					return;
				}

				const { selectedWorkspace, selectedProject, temporarySelectedProject } =
					useWorkspaceStore();
				workspaceStore.setSelectedProject(temporarySelectedProject);
				useRouter().push(`/w/${selectedWorkspace.id}/p/${selectedProject.id}/settings`);
			}
		},
		{
			label: useNuxtApp().$translate('GLOBAL.duplicate'),
			icon: mdiContentCopy,
			disabled: !targetDuplicateWorkspaceList.value.length,
			showTooltip: !targetDuplicateWorkspaceList.value.length,
			tooltipText: i18n.global.t('PROJECT.must_be_creator_in_workspace'),
			accessTypes: ['CREATOR', 'OWNER', 'BUSINESS'],
			onClick: () => {
				dialogStore.switchDialog('duplicateProjectModalController', true);
			}
		},
		{
			label: useNuxtApp().$translate('GLOBAL.share'),
			icon: mdiShare,
			hide: isSankhyaClient,
			accessTypes: ['CREATOR', 'OWNER', 'BUSINESS'],
			onClick: () => {
				dialogStore.switchDialog('invitationPanelController', true);
			}
		},
		{
			label: isCurrentKnowledgeBase.value
				? useNuxtApp().$translate('GLOBAL.remove_knowledge_base')
				: useNuxtApp().$translate('GLOBAL.make_knowledge_base'),
			icon: isCurrentKnowledgeBase.value ? mdiProgressClose : AiBrain04Icon,
			hide: isSankhyaClient || isDeveloperWorkspace.value,
			accessTypes: ['CREATOR', 'OWNER'],
			typeHuge: !isCurrentKnowledgeBase.value,
			onClick: () => {
				useDialogStore().switchDialog('knowledgeBaseController', true);
			}
		},

		// {
		// 	label: useNuxtApp().$translate('GLOBAL.make_store'),
		// 	hideOption: !isToShowMakeStoreToLoggedUser.value,
		// 	icon: mdiFolderPlusOutline,
		// 	disabled: !isToShowMakeStoreToLoggedUser.value,
		// 	showTooltip: !isToShowMakeStoreToLoggedUser.value,
		// 	tooltipText: i18n.global.t('PROJECT.must_be_creator_in_workspace'),
		// 	accessTypes: ['CREATOR', 'OWNER', 'BUSINESS'],
		// 	onClick: () => dialogStore.switchDialog('makeStoreProjectModalController', true)
		// },
		{
			label: useNuxtApp().$translate('WORKSPACES.manage_members'),
			icon: mdiAccountMultiple,
			class: 'm-0',
			hide: isSankhyaClient,
			onClick: async () => {
				const { selectedWorkspace, selectedProject, temporarySelectedProject } =
					useWorkspaceStore();
				await workspaceStore.setSelectedProject(
					temporarySelectedProject,
					workspaceStore.openedWorkSpaceId,
					false
				);
				useRouter().push(`/w/${selectedWorkspace.id}/p/${selectedProject.id}/members`);
			}
		},
		{
			label: useNuxtApp().$translate('GLOBAL.settings'),
			hide: isCorporate.value || isSankhyaClient,
			icon: mdiCog,
			// showTooltip: !isToShowMakeStoreToLoggedUser.value,
			// tooltipText: i18n.global.t('PROJECT.must_be_creator_in_workspace'),
			accessTypes: ['CREATOR', 'OWNER'],
			onClick: () => {
				const { selectedWorkspace, selectedProject, temporarySelectedProject } =
					useWorkspaceStore();
				workspaceStore.setSelectedProject(temporarySelectedProject);
				useRouter().push(`/w/${selectedWorkspace.id}/p/${selectedProject.id}/settings`);
			}
		},

		{ divider: true, class: 'm-0' },

		{
			label: useNuxtApp().$translate('WORKSPACES.delete_project'),
			icon: mdiDeleteOutline,
			accessTypes: ['CREATOR', 'OWNER'],
			onClick: () => {
				useDialogStore().switchDialog('deleteProjectController', true);
			}
		}
	]);

	async function openProject(project: Project, event: any) {
		if (useWhiteLabel().isSankhyaClient && renamingProject.value) {
			return;
		}

		const newTab = event?.metaKey;
		if (!newTab) {
			await workspaceStore.setSelectedProject(
				project,
				workspaceStore.openedWorkSpaceId,
				false
			);
			await useWorkspaceStore().refreshDBToken(project.id);
		}
		if (project.accessType === 'BUSINESS') {
			await useWorkspaceStore().fetchTenantConfig();
			openProjectPlayMode(project, newTab);
			return;
		}
		legacyStore.setSelectedModule(null);

		await workspaceStore.setSelectedProject(project);
		if ((event as any).target === 'settings?t=8')
			useRouter().push(
				`/w/${useWorkspaceStore().selectedWorkspace.id}/p/${project.id}/settings`
			);
	}

	function handleCardHover(hover: boolean, idx: number) {
		const dropdownEl = document.getElementById(`project-options-${idx}`);

		if (dropdownEl && !hover) {
			return;
		}

		cardHovered.value = hover ? idx : -1;
	}

	function handleDropdownVisibleChange(visible: boolean) {
		if (!visible) {
			cardHovered.value = -1;
		}
	}

	async function openProjectPlayMode(project: Project, newTab = true) {
		try {
			await workspaceStore.fetchTenantConfig();
		} catch {
			// eslint-disable-next-line no-console
			console.error('fail to fetch tenant config');
		}

		const selectedModule = legacyStore.selectedModule;
		const selectedScreenId = legacyStore.selectedScreenId;

		if (isSankhyaClient) {
			legacyStore.disposeLegacyStore();
		}

		const customAppConfig = workspaceStore.customAppConfig as any;

		if (project.dnsEnabled && isDeveloperWorkspace.value) {
			const app = await useMarketplacePublisher().findAppByDomain(
				project.projectDnsPublished as string,
				{
					validateRequested: false,
					onlyReturnData: true
				}
			);

			const hasPrimaryCustomDNS = app?.primaryCustomDNS?.length;
			const hasCustomDomainName = app?.customDomainName?.length;

			const address = hasCustomDomainName
				? app.customDomainName
				: hasPrimaryCustomDNS
				? app.primaryCustomDNS
				: app?.principalDomain;

			window.open(`https://${address}`, '_blank');
		} else {
			const homeScreen = !customAppConfig
				? ''
				: customAppConfig?.enableHomeAi || openPlaygroundMode?.value
				? '?playground'
				: `/m/${selectedModule?.id ?? 1}?screenId=${
						operational.isMobile
							? customAppConfig?.mobileHomeScreenId ??
							  customAppConfig?.appHomeScreenId ??
							  selectedScreenId.value ??
							  1
							: customAppConfig?.appHomeScreenId ?? selectedScreenId.value ?? 1
				  }&preview`;
			if (!customAppConfig?.value) {
				useWorkspaceStore().fetchTenantConfig();
				useAppStore().toggleAllowIaForPreview(true);
			}
			if (!newTab && !operational.isMobile) {
				workspaceStore.setSelectedProject(
					project,
					workspaceStore.openedWorkSpaceId,
					false
				);
				useRouter().push(
					`/w/${selectedWorkspace.value.id}/p/${project.id}${homeScreen}`
				);
				return;
			}

			window.open(
				`/w/${selectedWorkspace.value.id}/p/${project.id}?preview`,
				newTab ? '_blank' : '_self'
			);
		}
	}

	function openAccessController() {
		useDialogStore().switchDialog('studioAccessController', true);
	}

	function openRenameCustomSkill({ contextIdx }: { contextIdx: number }) {
		renamingSkillText.value = projectList.value[contextIdx].name;
		renamingSkillsIdx.value = contextIdx;
	}

	async function renameSkillByInput(project: Project) {
		if (
			renamingSkillText.value.trim() === '' ||
			renamingSkillText.value === project.name
		) {
			renamingSkillsIdx.value = -1;
			return;
		}

		try {
			renamingProject.value = true;
			await useProjectService().updateProject({
				...project,
				name: renamingSkillText.value
			});

			const projectIdx = selectedWorkspace.value.projects.findIndex(
				(p) => p.id === project.id
			);

			selectedWorkspace.value.projects[projectIdx].name = renamingSkillText.value;
		} catch (error) {
			toastNotify((error as CustomError).message ?? 'DATABASE.update_error');
		} finally {
			renamingSkillsIdx.value = -1;
			setTimeout(() => {
				renamingProject.value = false;
			}, 500);
		}
	}

	// const emit = defineEmits(['update:layoutType']);

	// const localLayoutType = computed({
	// 	get: () => props.layoutType,
	// 	set: (value: number) => emit('update:layoutType', value)
	// });

	function handleCardClick(project: Project, event: any) {
		operational.isMobile || openPlaygroundMode.value
			? openProjectPlayMode(project, false)
			: openProject(project, event[1]);
	}
</script>

<style scoped lang="postcss">
	.card {
		min-width: -webkit-fill-available;
		/* flex: 1 1 calc(25% - 1rem); */
		/* max-width: calc(25% - 1rem); */
		cursor: pointer;
		&-wrapper {
			/* padding-right: 0.3rem; */
			/* justify-content: space-between; */
			/* @media (max-width: 1475px) {
				justify-content: space-between;
			} */
		}
		&:hover {
			box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1),
				0px 2px 4px -2px rgba(16, 24, 40, 0.06);
		}
	}

	.workspace-content {
		width: 100%;
		padding: 32px 40px 0px 40px;
	}

	.animate-spin {
		animation: spin 3s linear infinite;
	}

	.simple-square {
		:deep(> div) {
			border-radius: 12px !important;
		}
	}

	.card--large {
		max-width: unset !important;
		width: 100%;
		flex: auto !important;
	}

	.card-wrapper {
		grid-template-columns: repeat(4, 1fr);
	}

	@media (max-width: 1800px) {
		.card-wrapper:not(.isCorporate) {
			grid-template-columns: repeat(3, 1fr);
		}
	}

	@media (max-width: 1400px) {
		.card-wrapper.isCorporate {
			grid-template-columns: repeat(3, 1fr);
		}
	}
	@media (max-width: 1500px) {
		.card-wrapper:not(.isCorporate) {
			grid-template-columns: repeat(2, 1fr);
		}
	}

	@media (max-width: 1100px) {
		.card-wrapper.isCorporate {
			grid-template-columns: repeat(2, 1fr);
		}
	}
	@media (max-width: 1200px) {
		.card-wrapper:not(.isCorporate) {
			grid-template-columns: repeat(1, 1fr);
		}

		.toggle-button {
			display: none;
		}
	}
	@media (max-width: 900px) {
		.card-wrapper.isCorporate {
			grid-template-columns: repeat(1, 1fr);
		}
	}
</style>
