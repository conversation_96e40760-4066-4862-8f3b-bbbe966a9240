/* eslint-disable no-console */
import { GoogleGenerativeAI } from '@google/generative-ai';
import axios from 'axios';
import { GLOBALS } from '~/helpers/contants/global_constants';

export default defineEventHandler(async (event) => {
	async function getUserGeminiApiKey(apiKeyPayload: any) {
		const runtimeBase =
			useRuntimeConfig().public.API_BASE_URL || 'https://api0.mitraecp.com:1005';
		const baseUrl: string =
			apiKeyPayload.baseUrl || apiKeyPayload.nuxtBaseUrl || runtimeBase;
		const payloadUserToken: string =
			apiKeyPayload.nuxtToken ??
			apiKeyPayload.authorizationToken ??
			apiKeyPayload.spaceToken ??
			'';
		if (!payloadUserToken) {
			throw createError({
				statusCode: 401,
				statusMessage: 'Missing authorization token'
			});
		}
		const isToUseUserIaCredit: boolean = apiKeyPayload.isToUseUserIaCredit ?? true;
		const isToUseOwnKey: boolean = apiKeyPayload.isToUseOwnKey ?? false;
		const selectedWorkspaceId: number =
			apiKeyPayload.selectedWorkspaceId ?? apiKeyPayload.workspaceId ?? 0;
		const freeIaCreditsUsage: number =
			apiKeyPayload.configChain?.freeIaCreditsUsage ??
			apiKeyPayload.freeIaCreditsUsage ??
			0;
		const isSankhya: boolean = apiKeyPayload.isSankhya ?? false;
		// console.log('baseUrl:', baseUrl);
		// console.log('payloadUserToken:', payloadUserToken);
		// console.log('isToUseUserIaCredit:', isToUseUserIaCredit);
		// console.log('isToUseOwnKey:', isToUseOwnKey);
		// console.log('selectedWorkspaceId:', selectedWorkspaceId);
		// console.log('freeIaCreditsUsage:', freeIaCreditsUsage);
		// console.log('isSankhya:', isSankhya);
		// Obter API key correta
		const apiKey = await getGeminiApiKey(
			baseUrl,
			payloadUserToken,
			isToUseUserIaCredit,
			isToUseOwnKey,
			selectedWorkspaceId,
			freeIaCreditsUsage,
			isSankhya
		);
		return apiKey;
	}
	async function getChainFromMitraAPI(chainEndpoint: string): Promise<string> {
		// BASEURL e Authorization hardcoded de um projeto existente no Mitra em https://app.mitralab.io/w/9300/p/14458/
		const mitraAxios = axios.create({
			baseURL: 'https://prod2.mitrasheet.com:8080/rest/v0',
			headers: {
				'Content-Type': 'application/json',
				Authorization: GLOBALS.API_CHAIN_TK
			}
		});

		// eslint-disable-next-line no-console
		console.log(`🔗 CHAIN DEBUG - Buscando CHAIN do endpoint: ${chainEndpoint}`);

		try {
			const response = await mitraAxios.get(`/${chainEndpoint}`);
			const chain = response.data;
			return chain?.content?.[0]?.[chainEndpoint.replaceAll(' ', '_')] || '';
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error('❌ CHAIN DEBUG - Erro ao buscar CHAIN:', error);
			return '';
		}
	}
	async function getCustomComponents() {
		try {
			const response = await axios.get(
				'https://prod2.mitrasheet.com:8080/rest/v0/Custom Components',
				{
					headers: {
						Authorization:
							'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJDaGFpbiBBdHVhbCIsIlgtVGVuYW50SUQiOiJ0ZW5hbnRfMjU5ODYifQ.I6kI6lVdTm3YV7bRQsR9cNiMXr79SHftlzdvDZRpSaoZD-WnOwHdOI8d0m5g_vzBHsdvvBu0FuT2izwkVudvog'
					}
				}
			);
			return response.data;
		} catch (error) {
			console.error('❌ Erro ao buscar Custom Components:', error);
			throw error;
		}
	}
	function replaceCustomComponents(
		chainReplaced: string,
		customComponents: any
	): string {
		// Depois, tratar os componentes individuais normalmente
		customComponents?.content?.forEach((component: any) => {
			const description = component['Descrição'];

			if (description) {
				// Criar regex para encontrar todos os padrões $DESCRIPTION.PROPERTY
				const descriptionRegex = new RegExp(`\\$${description}\\.(\\w+)`, 'g');

				// Encontrar todas as ocorrências e fazer replace
				chainReplaced = chainReplaced.replace(
					descriptionRegex,
					(match: string, property: string) => {
						// Verificar se a propriedade existe no componente
						if (
							property in component &&
							component[property] !== undefined &&
							component[property] !== null
						) {
							// console.log(`🔄 Substituindo ${match} por:`, component[property]);
							return component[property];
						}

						// Se não encontrar a propriedade, manter o texto original
						console.warn(`⚠️ Propriedade '${property}' não encontrada em ${description}`);
						return match;
					}
				);
			}
		});
		return chainReplaced;
	}
	async function generateContent(
		userPrompt: any[],
		componentPrompt: any,
		files: any[],
		genAI: any,
		chain: any
	) {
		const customComponents = await getCustomComponents();
		const firstKey = Object.keys(componentPrompt)?.[0];
		const componentPromises = userPrompt.map(
			async (promptData: { text: string; ref: number }) => {
				const component =
					componentPrompt?.[promptData.ref === 0 ? firstKey : promptData.ref] || '';
				const systemInstruction = component + chain;
				const model = genAI.getGenerativeModel({
					model: 'gemini-2.5-pro',
					systemInstruction
				});
				const prompt = replaceCustomComponents(promptData.text, customComponents);
				const contentParts: any[] = [{ text: prompt }];

				if (files && files.length > 0) {
					console.log('� GEMINI CODE - Processando arquivos:', files.length);

					for (const file of files) {
						if (file?.url && !file?.data) {
							try {
								const res = await fetch(file.url);
								const ab = await res.arrayBuffer();
								(file as any).data = Buffer.from(ab).toString('base64');
								(file as any).type =
									file.type || res.headers.get('content-type') || 'application/octet-stream';
							} catch {}
						}

						try {
							const base64Data = file.data || file.url;
							if (!base64Data) {
								console.warn(`⚠️ GEMINI CODE - Arquivo sem dados: ${file.name}`);
								continue;
							}

							const cleanBase64 = base64Data.includes('base64,')
								? base64Data.split('base64,')[1]
								: base64Data;

							const isValidBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64);
							if (!isValidBase64) {
								console.error(`❌ GEMINI CODE - Base64 inválido para ${file.name}`);
								continue;
							}

							const supportedTypes = [
								// Imagens
								'image/png',
								'image/jpeg',
								'image/jpg',
								'image/webp',
								'image/gif',
								// Documentos
								'application/pdf',
								'text/plain',
								'text/csv',
								'text/html',
								'text/css',
								'text/javascript',
								// Documentos Office
								'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
								'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
								'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
								// Outros
								'application/rtf',
								'text/rtf'
							];

							if (supportedTypes.includes(file.type)) {
								contentParts.push({
									inlineData: {
										data: cleanBase64,
										mimeType: file.type
									}
								});

								console.log(
									`✅ GEMINI CODE - Arquivo adicionado com sucesso: ${file.name} (${file.type})`
								);
							} else {
								console.warn(
									`⚠️ GEMINI CODE - Tipo de arquivo não suportado: ${file.type} para ${file.name}`
								);
							}
						} catch (error) {
							console.error('❌ GEMINI CODE - Erro ao processar arquivo:', error);
						}
					}
				}

				const result = await model.generateContent(contentParts);
				const response = await result.response;
				const text = response.text();
				return { ref: promptData.ref, text };
			}
		);
		try {
			const resolvedComponents = await Promise.all(componentPromises);
			const componentMap: { [key: number]: string } = {};
			resolvedComponents.forEach(({ ref, text }) => {
				componentMap[ref] = text;
			});
			return componentMap;
		} catch (error) {
			console.error(
				'❌ GEMINI CODE - Erro ao resolver promessas dos componentes:',
				error
			);
		}
	}
	try {
		const body = await readBody(event);

		const { userPrompt, componentPrompt, files } = body;

		if (!userPrompt || userPrompt.length === 0) {
			throw createError({
				statusCode: 400,
				statusMessage: 'System prompt is required'
			});
		}

		const apiKey = await getUserGeminiApiKey(body.apiKeyPayload);

		const chain = await getChainFromMitraAPI(
			body.isValidacao ? 'Chain AI Coder Validacao' : 'Chain AI Coder'
		);

		const genAI = new GoogleGenerativeAI(apiKey);

		const result = await generateContent(
			userPrompt,
			componentPrompt,
			files,
			genAI,
			chain
		);

		return {
			success: true,
			content: result
		};
	} catch (error: any) {
		// eslint-disable-next-line no-console
		console.error('❌ Erro na chamada Gemini Code:', error);

		throw createError({
			statusCode: 500,
			statusMessage: error.message || 'Erro interno do servidor'
		});
	}
});
