import { authService } from '~/services/AuthService';
/* eslint-disable no-console */
export const useGeminiCode = () => {
	function getApiKeyPayload() {
		const authStore = useAuthStore();
		const { merge } = storeToRefs(authStore);
		const TOKEN_LEGADO_MITRA = `Bearer ${merge?.value?.userToken}`;
		const nuxtBaseUrl = useRuntimeConfig().public.API_BASE_URL;
		const nuxtToken = authService.getToken();
		const isDeveloperWorkspace = useWorkspaceStore().isDeveloperWorkspace;
		const isToUseUserIaCredit = ref(false);
		if (!isDeveloperWorkspace) {
			const selectedProject = useWorkspaceStore().selectedProject;
			const loggedUserEmail = useUserStore().getLoggedUserEmail;

			const currentCollaborator = selectedProject.collaboratorProfile.find(
				(collab: any) => collab.email === loggedUserEmail
			);

			if (currentCollaborator) {
				isToUseUserIaCredit.value = currentCollaborator?.useIaCredit ?? true;
			} else {
				const selectedWorkspace = useWorkspaceStore().selectedWorkspace;
				const currentWorkspaceCollaborator = selectedWorkspace.collaboratorProfile.find(
					(collab: any) => collab.email === loggedUserEmail
				);

				if (currentWorkspaceCollaborator) {
					isToUseUserIaCredit.value = currentWorkspaceCollaborator.useIaCredit ?? true;
				}
			}
		} else {
			isToUseUserIaCredit.value = true;
		}

		const payload = {
			nuxtBaseUrl,
			nuxtToken,
			authorizationToken: TOKEN_LEGADO_MITRA,
			isToUseUserIaCredit: isToUseUserIaCredit.value,
			isToUseOwnKey: useSuperAIStore().userIsCorpAndUseOwnKey ?? false,
			selectedWorkspaceId: useWorkspaceStore().selectedWorkspace.id,
			freeIaCreditsUsage: useSuperAIStore().freeIaCreditsUsageServerSide,
			isSankhya: useWhiteLabel().isSankhyaClient
		};

		return payload;
	}
	async function getComponentPrompt(payloadPlan: any) {
		if (payloadPlan?.ALT_COMPONENT_HTML?.length) {
			try {
				const componentPromises = payloadPlan.ALT_COMPONENT_HTML.map(
					async (htmlComponent: { screenComponentId: number; ref: number }) => {
						const id = htmlComponent.screenComponentId;
						const ref = htmlComponent.ref;

						if (!id || ref === undefined) {
							return null;
						}

						try {
							const { component } = await useLegacyService().getComponentById(id);
							if (component) {
								return { ref, text: component.text };
							}
						} catch (err) {
							console.error(`Erro ao buscar componente por ID: ${id}`, err);
						}
						return null;
					}
				);

				const resolvedComponents = await Promise.all(componentPromises);

				return resolvedComponents.reduce((accumulator, currentResult) => {
					if (currentResult) {
						const { ref, text } = currentResult;

						accumulator[ref] = 'Vamos alterar o seguinte componente:\n\n' + text + '\n\n';
					}
					return accumulator;
				}, {} as Record<number, string>);
			} catch (err) {
				console.error('Erro geral ao processar componentes HTML:', err);
			}
		}
		return {};
	}
	const callGeminiCode = async (
		userPrompt: any,
		payloadPlan: any,
		files?: any[]
	) => {
		const componentPrompt = await getComponentPrompt(payloadPlan);

		const apiKeyPayload = await getApiKeyPayload();
		const payload = {
			apiKeyPayload,
			userPrompt,
			files: files || [],
			componentPrompt,
			isValidacao:
				useRuntimeConfig().public?.BACKEND_ENVIRONMENT_MAP === 'validacao' ||
				useRuntimeConfig().public?.BACKEND_ENVIRONMENT_MAP === 'validacao2'
		};
		try {
			const response = (await $fetch('/api/gemini-code', {
				method: 'POST',
				body: payload
			})) as any;

			if (response.success) {
				const messageId = `code-${Date.now()}`;
				await useAiService().createAILogMessage({
					projectId: useWorkspaceStore().selectedProject.id,
					messageId,
					message: response.content,
					threadId: messageId,
					messageType: 'SUPERIA_RESPONSE'
				});
				// eslint-disable-next-line no-console
				console.log('> SystemInstruction:', response.systemInstruction);
				// eslint-disable-next-line no-console
				console.log('> Input:', response.prompt);
				// eslint-disable-next-line no-console
				console.log('> Output:', response.content);
				return response.content;
			} else {
				throw new Error('Resposta inválida do Gemini Code');
			}
		} catch (error: any) {
			console.error('Erro no useGeminiCode:', error);
			throw error;
		}
	};

	return {
		callGeminiCode
	};
};
